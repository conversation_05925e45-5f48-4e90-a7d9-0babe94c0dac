@extends('master')

@section('title')
    Profile
@endsection

@section('header')

@endsection

@section('page_id')
    page-profile-users
@endsection

@section('page_content')

    <!-- Main Content -->
    <div class="container-fluid py-4">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col">
                <h1 class="h3 mb-0" id="pageTitle">Your Profile</h1>
                <p class="text-muted" id="pageDescription">Update your account account</p>
            </div>
            <div class="col-auto">
                <a href="{{ url('/worklist') }}" class="btn btn-secondary">
                    <i class="bi bi-arrow-left me-1"></i>Go to Worklist
                </a>
            </div>
        </div>

        <!-- User Form -->
        <div class="row">
            <div class="col-12">
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="bi bi-shield-check me-2"></i>Information
                        </h6>
                    </div>
                    <div class="card-body">
                        <form id="adminUserForm" method="POST" action="{{ url('profile') }}" enctype="multipart/form-data">

                            @csrf

                            <div class="row">
                                <div class="col-md-12 mb-3">
                                    <label for="adminFullName" class="form-label fw-bold">Your Name <span class="text-danger">*</span></label>
                                    <input disabled value="{{ $user->name }}" type="text" class="form-control" id="adminFullName" name="name" required>
                                </div>
                                <div class="col-md-12 mb-3">
                                    <label for="adminUserId" class="form-label fw-bold">Your ID <span class="text-danger">*</span></label>
                                    <input disabled value="{{ $user->username }}" type="text" placeholder="Write user ID without any spaces" class="form-control" name="username" id="adminUserId" required>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-12 mb-3">
                                    <label for="adminPassword" class="form-label fw-bold">Password (Enter if you want to change)</label>
                                    <input type="password" class="form-control" id="adminPassword" name="password">
                                </div>
                                <div class="col-md-12 mb-3">
                                    <label for="adminConfirmPassword" class="form-label fw-bold">Confirm Password</label>
                                    <input type="password" class="form-control" id="adminConfirmPassword" name="confirm_password">
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-12 mb-3">
                                    <label for="adminMobileNo" class="form-label fw-bold">Mobile No <span class="text-danger">*</span></label>
                                    <input value="{{ $user->mobile }}" type="tel" class="form-control" id="adminMobileNo" name="mobile" required>
                                </div>
                                <div class="col-md-12 mb-3">
                                    <label for="tenantEmail" class="form-label fw-bold">Email</label>
                                    <input value="{{ $user->email }}" type="email" class="form-control" id="tenantEmail" name="email">
                                </div>
                            </div>

                            @if(auth()->user()->type == \App\Constants\UserType::MANAGER)
                            <!-- Identity Information -->
                            <div class="row">
                                <div class="col-md-12 mb-3">
                                    <label for="identityInfo" class="form-label fw-bold">Identity Information</label>
                                    <textarea disabled class="form-control" id="identityInfo" name="identity_info" rows="4">{{ $user->identity_info }}</textarea>
                                </div>

                                <!-- Signature Upload -->
                                <div class="col-md-12 mb-3">
                                    <label class="form-label fw-bold">Signature</label>
                                    <div class="mt-2">
                                        <input type="file" class="form-control" id="signatureUpload" name="signature" accept="image/*" onchange="previewSignature(this)">
                                        <small class="form-text text-muted">Upload signature image (JPG, PNG, GIF)</small>

                                    </div>
                                </div>

                                <div class="col-md-12 mb-3">
                                    <!-- Signature Preview -->
                                    <div id="signaturePreview" class="mt-3" style="display: @if($user->signature) block @else none @endif;">
                                        <div class="d-flex align-items-center">
                                            <img id="signatureImage" src="{{ config('filesystems.base_url') . $user->signature }}" alt="Signature Preview" style="max-width: 200px; max-height: 100px; border: 1px solid #ddd; padding: 5px;">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            @endif

                            <div class="text-start mb-4">
                                <button type="submit" class="btn btn-primary" id="submitBtn">
                                    <i class="bi bi-person-plus me-1"></i><span id="submitText">Update</span>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

@endsection

@section('footer')
    <script>
        // Signature upload functionality
        function previewSignature(input) {
            if (input.files && input.files[0]) {
                const reader = new FileReader();

                reader.onload = function(e) {
                    document.getElementById('signatureImage').src = e.target.result;
                    document.getElementById('signaturePreview').style.display = 'block';
                }

                reader.readAsDataURL(input.files[0]);
            }
        }
    </script>
@endsection
