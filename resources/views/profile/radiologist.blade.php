@extends('master')

@section('title')
    Profile
@endsection

@section('header')

@endsection

@section('page_id')
    page-profile-radiologist
@endsection

@section('page_content')

    <!-- Main Content -->
    <div class="container-fluid py-4">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col">
                <h1 class="h3 mb-0" id="pageTitle">Your Profile</h1>
                <p class="text-muted" id="pageDescription">Update your account account</p>
            </div>
            <div class="col-auto">
                <a href="{{ url('/worklist') }}" class="btn btn-secondary">
                    <i class="bi bi-arrow-left me-1"></i>Go to Worklist
                </a>
            </div>
        </div>

        <!-- Radiologist Form -->
        <div class="row">
            <div class="col-12">
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="bi bi-person-badge me-2"></i>Radiologist Information
                        </h6>
                    </div>
                    <div class="card-body">
                        <form id="radiologistUserForm" method="POST" action="{{ url('profile') }}" enctype="multipart/form-data">

                            @csrf

                            <div class="row">
                                <div class="col-md-12 mb-3">
                                    <label for="consultantName" class="form-label fw-bold">Your Name <span class="text-danger">*</span></label>
                                    <input disabled value="{{ $user->name }}" type="text" class="form-control" id="consultantName" name="name" required>
                                </div>
                                <div class="col-md-12 mb-3">
                                    <label for="tenantShortName" class="form-label fw-bold">Your ID <span class="text-danger">*</span></label>
                                    <input disabled value="{{ $user->username }}" type="text" placeholder="Write user ID without any spaces" class="form-control" name="username" required>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-12 mb-3">
                                    <label for="radiologistEmail" class="form-label fw-bold">Email</label>
                                    <input value="{{ $user->email }}" type="email" class="form-control" id="radiologistEmail" name="email">
                                </div>
                                <div class="col-md-12 mb-3">
                                    <label for="radiologistPhone" class="form-label fw-bold">Phone No</label>
                                    <input value="{{ $user->mobile }}" type="tel" class="form-control" id="radiologistPhone" name="mobile">
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-12 mb-3">
                                    <label for="identityInfo" class="form-label fw-bold">Identity Information</label>
                                    <textarea disabled class="form-control" id="identityInfo" name="identity_info" rows="4">{{ $user->identity_info }}</textarea>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-12 mb-3">
                                    <label for="radiologistPassword" class="form-label fw-bold">Password (Enter if you want to change)</label>
                                    <input type="password" class="form-control" id="radiologistPassword" name="password">
                                </div>
                                <div class="col-md-12 mb-3">
                                    <label for="radiologistConfirmPassword" class="form-label fw-bold">Confirm Password</label>
                                    <input type="password" class="form-control" id="radiologistConfirmPassword" name="confirm_password">
                                </div>
                            </div>

                            <!-- Allowed Modalities -->
                            <div class="row" style="display: @if($user->is_internal == 0) block @else none @endif;">
                                <div class="col-md-12 mb-3">
                                    <label class="form-label fw-bold">Allowed Modalities & Rates</label>
                                    <div class="mt-2">

                                        @foreach ($modalities as $key => $modality)
                                            <div class="row mb-2">
                                                <div class="col-md-7">
                                                    <div class="row">
                                                        <div class="col-md-8">
                                                            <div class="form-check">
                                                                <label class="form-check-label" for="modality">{{ $modality }}</label>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-4">
                                                            <div class="input-group input-group-sm">
                                                                <span class="input-group-text">BDT</span>
                                                                <input disabled value="{{ $user->modalities_n_rates->{$key} ?? 0 }}" type="number" class="form-control" name="rates[{{ $key }}]" placeholder="Rate" min="0" step="1" required>
                                                            </div>
                                                        </div>
                                                        <hr style="margin: 10px 0 0 0; padding: 0">
                                                    </div>
                                                </div>
                                            </div>
                                        @endforeach

                                    </div>
                                </div>
                            </div>

                            <!-- BMDC Reg No and Status -->
                            <div class="row">

                                <div class="col-md-12 mb-3">
                                    <label for="bmdcRegNo" class="form-label fw-bold">BMDC Reg No</label>
                                    <input disabled value="{{ $user->bmdc_number }}" type="text" class="form-control" id="bmdcRegNo" name="bmdc_number">
                                </div>

                            </div>

                            <!-- Signature Upload -->
                            <div class="row">
                                <div class="col-md-12 mb-3">
                                    <label class="form-label fw-bold">Signature</label>
                                    <div class="mt-2">
                                        <input type="file" class="form-control" id="signatureUpload" name="signature" accept="image/*" onchange="previewSignature(this)">
                                        <small class="form-text text-muted">Upload signature image (JPG, PNG, GIF)</small>

                                    </div>
                                </div>

                                <div class="col-md-12 mb-3">
                                    <!-- Signature Preview -->
                                    <div id="signaturePreview" class="mt-3" style="display: @if($user->signature) block @else none @endif;">
                                        <div class="d-flex align-items-center">
                                            <img id="signatureImage" src="{{ config('filesystems.base_url') . $user->signature }}" alt="Signature Preview" style="max-width: 200px; max-height: 100px; border: 1px solid #ddd; padding: 5px;">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="text-start mb-4">
                                <button type="submit" class="btn btn-primary" id="submitBtn">
                                    <i class="bi bi-person-plus me-1"></i><span id="submitText">Update</span>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

@endsection

@section('footer')
    <script>
        // Signature upload functionality
        function previewSignature(input) {
            if (input.files && input.files[0]) {
                const reader = new FileReader();

                reader.onload = function(e) {
                    document.getElementById('signatureImage').src = e.target.result;
                    document.getElementById('signaturePreview').style.display = 'block';
                }

                reader.readAsDataURL(input.files[0]);
            }
        }
    </script>
@endsection
