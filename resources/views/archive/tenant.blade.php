@extends('master')

@section('title')
    Archive
@endsection

@section('header')

@endsection

@section('page_id')
    page-tenant-archive
@endsection

@section('page_content')

    <div class="container-fluid py-4">

        <!-- Search Filter Block -->
        <div class="row mb-4">
            <div class="col-12">
                <form action="{{ url('archive') }}" method="GET">
                <div class="card shadow-sm filter-block">
                    <div class="card-body py-3">
                        <div class="row align-items-center">
                            <div class="col-auto">
                                <h6 class="mb-0 text-muted">
                                    <i class="bi bi-funnel me-2"></i>Filters:
                                </h6>
                            </div>
                            {{-- <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label for="radiologistFilter" class="form-label small text-muted mb-1">Radiologist</label>
                                    <select class="form-select" id="radiologistFilter" name="radiologist">
                                        <option value="">All Radiologists</option>
                                        @foreach($radiologists as $radiologist)
                                            <option value="{{ $radiologist->id }}" @if($radiologist->id == request('radiologist')) selected @endif>{{ $radiologist->name }}</option>
                                        @endforeach
                                    </select>
                                </div>
                            </div> --}}
                            <div class="col-md-4 col-sm-6">
                                <div class="form-group">
                                    <label for="patientIdFilter" class="form-label small text-muted mb-1">Patient ID</label>
                                    <input type="text" class="form-control" id="patientIdFilter" name="patient_id" value="{{ request('patient_id') }}" placeholder="Enter Patient ID">
                                </div>
                            </div>
                            <div class="col-md-2 col-sm-6">
                                <div class="form-group">
                                    <label class="form-label small text-muted mb-1">&nbsp;</label>
                                    <button type="submit" class="btn btn-primary w-100" id="applyFilters">
                                        <i class="bi bi-search me-1"></i>Search
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                </form>
            </div>
        </div>

        <!-- Worklist Items -->
        <div class="row">
            <div class="col-12">
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <div class="row align-items-center">
                            <div class="col-md-4">
                                <h6 class="m-0 font-weight-bold text-primary">
                                    <i class="bi bi-list-task me-2"></i>Archived Items
                                </h6>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover table-striped default-table" id="worklistTable">
                                <thead class="table-dark">
                                <tr>
                                    <th width="60">SL</th>
                                    <th width="60">View</th>
                                    <th>Print</th>
                                    <th>Patient ID</th>
                                    <th>Patient Name</th>
                                    <th>Procedure</th>
                                    <th>Clinical History</th>
                                    <th>Arrival Date/Time</th>
                                    <th>Modality</th>
                                    <th>Radiologist</th>
                                    <th>Order Time</th>
                                    <th>Unarchive</th>
                                </tr>
                                </thead>
                                <tbody>

                                @forelse($worklist as $item)
                                    <tr>
                                        <td>{{ $worklist->total() - ($loop->iteration + $worklist->perPage() * ($worklist->currentPage() - 1)) + 1 }}</td>
                                        <td>
                                            <div class="d-flex gap-1">
                                                @php
                                                    $file_links = json_decode($item->file_links, true);
                                                    if(json_last_error() === JSON_ERROR_NONE && is_array($file_links)){
                                                        $first_file = $file_links[0];
                                                        $multifile = 'yes';
                                                    }else{
                                                        $first_file = $item->file_links;
                                                        $multifile = 'no';
                                                    }
                                                @endphp

                                                @if($item->file_type == 'DICOM')
                                                    <a href="{{ url('worklist/'.$item->id).'/dcm-view?file=' . base64_encode(config('filesystems.base_url') . $first_file) . '&files=' . base64_encode($item->file_links) . '&multifile=' . $multifile . '&item=' . $item->id }}" class="btn btn-sm btn-dark x-ray-btn" style="background-color: #0a52b8;" title="View DICOM">
                                                        <img src="{{ asset('assets/img/ico-xray-dicom.svg') }}" alt="DICOM" style="width: 24px; height: 24px;">
                                                    </a>
                                                @else
                                                    <a href="{{ url('worklist/'.$item->id).'/dcm-view?file=' . base64_encode(config('filesystems.base_url') . $first_file) . '&files=' . base64_encode($item->file_links) . '&multifile=' . $multifile . '&item=' . $item->id }}" class="btn btn-sm btn-dark x-ray-btn" style="background-color: #333;" title="View Image">
                                                        <img src="{{ asset('assets/img/ico-xray-img.svg') }}" alt="Image" style="width: 24px; height: 24px;">
                                                    </a>
                                                @endif
                                            </div>
                                        </td>
                                        <td>
                                            <a href="{{ url('worklist/'.$item->id.'/print') }}" class="btn btn-sm btn-outline-success bg-success text-white" title="Write Report with Images">
                                                <i class="bi bi-printer"></i>
                                            </a>
                                        </td>
                                        <td><strong>{{ $item->patient_id }}</strong></td>
                                        <td>{{ $item->patient_name }}</td>
                                        <td>
                                            {{ $item->procedure == 0 ? '' : ($item->procedure ?? '') }}
                                            <hr style="margin: 0;">
                                            @php
                                                $procedure_history = $item->procedure_history;
                                                if ($procedure_history == '@') {
                                                    $procedure_history = '';
                                                } elseif ($procedure_history) {
                                                    // Remove "0;" patterns from the string
                                                    $cleaned = preg_replace('/0;\s*/', '', $procedure_history);
                                                    // If after cleaning we only have whitespace/semicolons, show N/A
                                                    $procedure_history = preg_match('/^[;\s]*$/', $cleaned) ? 'N/A' : $cleaned;
                                                } else {
                                                    $procedure_history = '-';
                                                }
                                            @endphp
                                            <span>{{ $procedure_history }}</span>
                                        </td>
                                        @php
                                            $clinical_history = $item->clinical_history;
                                            if ($clinical_history) {
                                                // Remove "0;" patterns from the string
                                                $cleaned = preg_replace('/0;\s*/', '', $clinical_history);
                                                // If after cleaning we only have whitespace/semicolons, show N/A
                                                $clinical_history = preg_match('/^[;\s]*$/', $cleaned) ? 'N/A' : $cleaned;
                                            } else {
                                                $clinical_history = 'N/A';
                                            }
                                        @endphp
                                        <td>{{ $clinical_history }}</td>
                                        <td>{{ date('d M Y h:i A', strtotime($item->study_time)) }}</td>
                                        <td>{{ $item->modality }}</td>
                                        <td>
                                            <i class="bi bi-person-check me-1"></i>{{ $item->completedBy->name }}
                                        </td>
                                        <td>{{ $item->assigned_at ? date('d M Y h:i A', strtotime($item->assigned_at)) : '-' }}</td>
                                        <td>
                                            <button type="button"
                                                    class="btn btn-sm btn-outline-primary unarchive-btn"
                                                    data-id="{{ $item->id }}"
                                                    data-bs-toggle="tooltip"
                                                    data-bs-placement="top"
                                                    title="Unarchive this item">
                                                <i class="bi bi-arrow-up-circle"></i> Unarchive
                                            </button>
                                        </td>
                                    </tr>
                                    @empty
                                        <tr>
                                            <td colspan="12" class="text-center">No Worklist Found</td>
                                        </tr>
                                @endforelse

                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <div class="d-flex justify-content-center">
                            {{ $worklist->appends(request()->query())->links() }}
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>

@endsection

@section('footer')
    <script>
        $(document).ready(function() {
            // Handle unarchive button click
            $(document).on('click', '.unarchive-btn', function() {
                const itemId = $(this).data('id');
                const button = $(this);

                Swal.fire({
                    title: 'Are you sure?',
                    text: "This item will be moved back to the active worklist.",
                    icon: 'question',
                    showCancelButton: true,
                    confirmButtonColor: '#3085d6',
                    cancelButtonColor: '#d33',
                    confirmButtonText: 'Yes, Unarchive',
                    cancelButtonText: 'Cancel',
                    reverseButtons: true
                }).then((result) => {
                    if (result.isConfirmed) {
                        // Show loading state
                        button.prop('disabled', true).html('<i class="bi bi-arrow-repeat spinner-border spinner-border-sm"></i> Processing...');

                        // Make AJAX call
                        $.ajax({
                            url: '{{ route("archive.unarchive") }}',
                            type: 'POST',
                            data: {
                                _token: '{{ csrf_token() }}',
                                id: itemId
                            },
                            success: function(response) {
                                if (response.success) {
                                    Swal.fire(
                                        'Success!',
                                        'Item has been unarchived successfully.',
                                        'success'
                                    ).then(() => {
                                        location.reload();
                                    });
                                } else {
                                    Swal.fire(
                                        'Error!',
                                        response.message || 'Failed to unarchive item.',
                                        'error'
                                    );
                                    button.prop('disabled', false).html('<i class="bi bi-arrow-up-circle"></i> Unarchive');
                                }
                            },
                            error: function(xhr) {
                                let errorMessage = 'An error occurred while unarchiving the item.';
                                if (xhr.responseJSON && xhr.responseJSON.message) {
                                    errorMessage = xhr.responseJSON.message;
                                }
                                Swal.fire(
                                    'Error!',
                                    errorMessage,
                                    'error'
                                );
                                button.prop('disabled', false).html('<i class="bi bi-arrow-up-circle"></i> Unarchive');
                            }
                        });
                    }
                });
            });
        });
    </script>
@endsection
