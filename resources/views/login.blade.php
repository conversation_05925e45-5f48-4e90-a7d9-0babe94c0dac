<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BMS System</title>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{{ asset('assets/img/favicon.ico') }}">

    <!-- Web App Manifest -->
    <link rel="manifest" href="{{ asset('assets/js/manifest.json') }}">

    <!-- Bootstrap 5.3.6 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.6/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-4Q6Gf2aSP4eDXB8Miphtr37CMZZQ5oXLH2yaXMJ2w8e2ZtHTl7GptT4jmndRuHDT" crossorigin="anonymous">

    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css">

    <!-- Custom CSS -->
    <link href="{{ url()->asset('assets/css/custom.css') }}" rel="stylesheet">

</head>
<body class="login">


<div class="container-fluid vh-100">
    <div class="row h-100">
        <!-- Left side - Login Form -->
        <div class="col-md-12 d-flex align-items-center justify-content-center">
            <div class="card shadow-lg border-0" style="width: 100%; max-width: 400px;">
                <div class="card-body p-5">
                    <!-- Logo/Brand -->
                    <div class="text-center mb-4">
                        <img src="{{ asset('assets/img/logo.svg') }}" alt="BMS Logo" style="height: 60px; width: auto;" class="mb-3">
                        <p class="text-muted">Access to Dashboard</p>
                    </div>

                    <!-- Login Form -->
                    <form id="loginForm" method="POST" action="{{ url('auth/login') }}">

                        @csrf

                        <div class="mb-3">
                            <label for="user_id" class="form-label">User ID</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="bi bi-person"></i>
                                </span>
                                <input value="@if($errors->has('old_user_id')){{ $errors->first('old_user_id') }}@endif" type="text" class="form-control" id="user_id" name="user_id" required>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="password" class="form-label">Password</label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="bi bi-lock"></i>
                                </span>
                                <input type="password" class="form-control" id="password" name="password" required>
                                <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                    <i class="bi bi-eye"></i>
                                </button>
                            </div>
                        </div>

                        <div class="mb-3">
                        @if($errors->has('old_user_id'))
                            <span class="label border-danger text-danger">User ID or Password is incorrect</span>
                        @endif
                        </div>

                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="bi bi-box-arrow-in-right me-2"></i>Sign In
                            </button>
                        </div>

                    </form>
                </div>
            </div>
        </div>

        <!-- Right side - Background/Image -->
        {{-- <div class="col-md-6 d-none d-md-flex align-items-center justify-content-center bg-primary">
            <div class="text-center text-white">
                <i class="bi bi-graph-up display-1 mb-4"></i>
                <h3 class="fw-bold">Welcome to BMS</h3>
                <p class="lead">Streamline your business operations with our comprehensive management system.</p>
                <div class="row mt-5">
                    <div class="col-4">
                        <i class="bi bi-speedometer2 display-6"></i>
                        <p class="mt-2">Dashboard</p>
                    </div>
                    <div class="col-4">
                        <i class="bi bi-bar-chart display-6"></i>
                        <p class="mt-2">Analytics</p>
                    </div>
                    <div class="col-4">
                        <i class="bi bi-shield-check display-6"></i>
                        <p class="mt-2">Security</p>
                    </div>
                </div>
            </div>
        </div> --}}
    </div>
</div>

<!-- Bootstrap 5.3.6 JS Bundle -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.6/dist/js/bootstrap.bundle.min.js" integrity="sha384-j1CDi7MgGQ12Z7Qab0qlWQ/Qqz24Gc6BM0thvEMVjHnfYGF0rmFCozFSxQBxwHKO" crossorigin="anonymous"></script>

<!-- Custom JS -->
<script src="{{ url()->asset('assets/js/custom.js') }}"></script>
<script>
    document.getElementById('togglePassword').addEventListener('click', function() {
        const password = document.getElementById('password');
        const icon = this.querySelector('i');

        if (password.type === 'password') {
            password.type = 'text';
            icon.classList.remove('bi-eye');
            icon.classList.add('bi-eye-slash');
        } else {
            password.type = 'password';
            icon.classList.remove('bi-eye-slash');
            icon.classList.add('bi-eye');
        }
    });
</script>

</body>
</html>
