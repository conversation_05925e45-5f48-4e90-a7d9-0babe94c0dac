@extends('master')

@section('title')
    Print
@endsection

@section('header')

@endsection

@section('page_id')
    page-print
@endsection

@section('page_content')

    <!-- Main Content -->
    <div class="container-fluid py-4">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col">
                <div class="d-flex align-items-center justify-content-between">
                    <div class="d-flex align-items-center">
                        <a href="{{ url('/worklist') }}" class="btn btn-outline-secondary me-3">
                            <i class="bi bi-arrow-left me-1"></i>Back to Worklist
                        </a>
                        <div>
                            <h1 class="h3 mb-0">Print Report</h1>
                            <p class="text-muted mb-0">Patient: {{ $report->patient_name }} ({{ $report->patient_id }})</p>
                        </div>
                    </div>
                    <div class="d-flex align-items-center gap-2">
                        <button type="button" class="btn btn-outline-info" data-bs-toggle="modal" data-bs-target="#printHelpModal" title="Print Help">
                            <i class="bi bi-question-circle"></i> Help
                        </button>
                        <button class="btn btn-primary" id="printBtn">
                            <i class="bi bi-printer me-1"></i>Print
                        </button>
                        <button class="btn btn-success" id="downloadDocBtn">
                            <i class="bi bi-download me-1"></i>Download .doc
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Print Content -->
        <div class="row">
            <div class="col-12">
                <div class="card shadow" id="printableContent">
                    <div class="card-body p-5">
                        <!-- Report Header -->
                        <div class="report-header mb-2">
                            <div class="header-box text-center p-1 border border-2 border-dark rounded">
                                <h2 class="fw-bold text-uppercase mb-0" style="color: #2c5530; font-size: 24px; letter-spacing: 2px;">
                                    DEPARTMENT OF RADIOLOGY & IMAGING
                                </h2>
                            </div>
                        </div>

                        <!-- Patient Information for Screen & Print -->
                        <div class="patient-info-section mb-4 screen-print-only">
                            <div class="patient-info-box border border-2 border-dark rounded p-2">

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="info-row">
                                            <span class="info-label">Id No.</span>
                                            <span class="info-separator">:</span>
                                            <span class="info-value fw-bold">{{ $report->patient_id }}</span>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="info-row">
                                            <span class="info-label">Receive</span>
                                            <span class="info-separator">:</span>
                                            <span class="info-value">{{ date('d/m/y h:i A', strtotime($report->study_time)) }}</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="info-row">
                                            <span class="info-label">Patient's Name</span>
                                            <span class="info-separator">:</span>
                                            <span class="info-value fw-bold">{{ $report->patient_name }}</span>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="info-row">
                                            <span class="info-label">Print</span>
                                            <span class="info-separator">:</span>
                                            <span class="info-value">{{ date('d/m/y h:i A') }}</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="info-row">
                                            <span class="info-label">Age</span>
                                            <span class="info-separator">:</span>
                                            <span class="info-value fw-bold">{{ $report->patient_age == '0' ? '-' : preg_replace('/^0+/', '', $report->patient_age) }}</span>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="info-row">
                                            <span class="info-label">Sex</span>
                                            <span class="info-separator">:</span>
                                            <span class="info-value fw-bold">
                                                @if(strtoupper(trim($report->patient_gender)) == 'F')
                                                    Female
                                                @elseif(strtoupper(trim($report->patient_gender)) == 'M')
                                                    Male
                                                @else
                                                    {{ ucfirst(strtolower(trim($report->patient_gender))) }}
                                                @endif
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="info-row">
                                            <span class="info-label">Refd. by</span>
                                            <span class="info-separator">:</span>
                                            <span class="info-value fw-bold">{{ $report->doctor?->name ?? '' }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Patient Information Table for .doc Download Only -->
                        <div class="patient-info-table-doc doc-only" style="display: none; margin-bottom: 50px;">
                            <table style="width: 100%; border-collapse: collapse; border: 2px solid #000; margin: 15px 0 50px 0;">
                                <tbody style="padding: 0; margin: 0;">
                                    <tr style="padding: 0; margin: 0; line-height: 1;">
                                        <td style="padding: 0 10px; font-size: 14px; width: 25%; font-style: italic; line-height: 1; margin: 0;">Id No.</td>
                                        <td style="padding: 0 10px; font-size: 14px; width: 25%; font-weight: bold; line-height: 1; margin: 0;">: {{ $report->patient_id }}</td>
                                        <td style="padding: 0 10px; font-size: 14px; width: 25%; font-style: italic; line-height: 1; margin: 0;">Receive</td>
                                        <td style="padding: 0 10px; font-size: 14px; width: 25%; font-weight: bold; line-height: 1; margin: 0;">: {{ date('d/m/y h:i A', strtotime($report->study_time)) }}</td>
                                    </tr>
                                    <tr style="padding: 0; margin: 0; line-height: 1;">
                                        <td style="padding: 0 10px; font-size: 14px; width: 25%; font-style: italic; line-height: 1; margin: 0;">Patient's Name</td>
                                        <td style="padding: 0 10px; font-size: 14px; width: 25%; font-weight: bold; line-height: 1; margin: 0;">: {{ $report->patient_name }}</td>
                                        <td style="padding: 0 10px; font-size: 14px; width: 25%; font-style: italic; line-height: 1; margin: 0;">Print</td>
                                        <td style="padding: 0 10px; font-size: 14px; width: 25%; font-weight: bold; line-height: 1; margin: 0;">: {{ date('d/m/y h:i A') }}</td>
                                    </tr>
                                    <tr style="padding: 0; margin: 0; line-height: 1;">
                                        <td style="padding: 0 10px; font-size: 14px; width: 25%; font-style: italic; line-height: 1; margin: 0;">Age</td>
                                        <td style="padding: 0 10px; font-size: 14px; width: 25%; font-weight: bold; line-height: 1; margin: 0;">: {{ $report->patient_age }}</td>
                                        <td style="padding: 0 10px; font-size: 14px; width: 25%; font-style: italic; line-height: 1; margin: 0;">Sex</td>
                                        <td style="padding: 0 10px; font-size: 14px; width: 25%; font-weight: bold; line-height: 1; margin: 0;">:
                                            @if(strtoupper(trim($report->patient_gender)) == 'F')
                                                Female
                                            @elseif(strtoupper(trim($report->patient_gender)) == 'M')
                                                Male
                                            @else
                                                {{ ucfirst(strtolower(trim($report->patient_gender))) }}
                                            @endif
                                        </td>
                                    </tr>
                                    <tr style="padding: 0; margin: 0; line-height: 1;">
                                        <td style="padding: 0 10px; font-size: 14px; width: 25%; font-style: italic; line-height: 1; margin: 0;">Refd. by</td>
                                        <td colspan="3" style="padding: 0 10px; font-size: 14px; width: 25%; font-weight: bold; line-height: 1; margin: 0;">: {{ $report->doctor?->name ?? 'SELF' }}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <!-- Report Content -->
                        <div class="mb-4 report-content mt-5">
                            <div class="cus-linebreak pt-4 pb-4" style="white-space: pre-line;">{{ $report->report_feedback }}</div>
                        </div>

                        <!-- Digital Signature -->
                        <div class="mt-4 text-start signature">
                            <div class="d-inline-block text-left">
                                @if($report->completedBy->signature)
                                <p class="mb-1">
                                    <img src="{{ config('filesystems.base_url') . $report->completedBy->signature }}" class="img-fluid" style="max-height: 60px;">
                                </p>
                                @endif
                                <p class="mb-1 fw-bold">{{ $report->completedBy->name ?? '-' }}</p>
                                <p class="text-muted small cus-linebreak" style="white-space: pre-line;">{{ $report->completedBy->identity_info ?? '' }}
                                    @if($report->completedBy->bmdc_number)
                                        BMDC REG NO. {{ $report->completedBy->bmdc_number }}
                                    @endif
                                </p>
                            </div>
                        </div>


                    </div>
                </div>
            </div>
        </div>
    </div>

@endsection

@section('footer')
    <!-- Print Styles -->
    <style>
        /* Screen Styles */
        .info-row {
            display: flex;
            align-items: center;
        }

        .info-label {
            min-width: 120px;
            font-style: italic;
            color: #666;
        }

        .info-separator {
            margin: 0 8px;
            color: #666;
        }

        .info-value {
            flex: 1;
            color: #333;
        }

        /* Hide doc-only elements on screen */
        .doc-only {
            display: none !important;
        }



        @media print {
            @page {
                margin-top: 170px;
                margin-bottom: 95px;
                margin-left: 50px;
                margin-right: 50px;
            }

            body * {
                visibility: hidden;
            }

            #printableContent, #printableContent * {
                visibility: visible;
            }

            #printableContent {
                position: absolute;
                left: 0;
                top: 0;
                width: 100%;
            }

            .navbar, .btn, .card-shadow {
                display: none !important;
            }

            /* Hide doc-only elements in print */
            .doc-only {
                display: none !important;
            }

            .card {
                border: none !important;
                box-shadow: none !important;
                margin: 0 !important;
            }

            .card-body {
                padding: 0 !important;
                margin: 0 !important;
            }

            /* Header Styles */
            .report-header {
                margin-bottom: 10px !important;
            }

            .header-box {
                border: 2px solid #000 !important;
                background: none !important;
                padding: 10px !important;
                margin: 0 !important;
            }

            .header-box h2 {
                font-size: 18px !important;
                color: #000 !important;
                margin: 0 !important;
            }

            /* Patient Info Styles */
            .patient-info-section {
                margin-bottom: 15px !important;
            }

            .patient-info-box {
                border: 2px solid #000 !important;
                background: none !important;
                /* padding: 15px !important; */
                margin: 0 !important;
            }

            .row {
                display: flex !important;
                flex-wrap: wrap !important;
                margin: 0 !important;
            }

            .col-md-6 {
                flex: 0 0 50% !important;
                max-width: 50% !important;
                padding: 0 10px !important;
            }

            .info-row {
                display: flex !important;
                /* margin-bottom: 6px !important; */
                font-size: 14px !important;
            }

            .info-label {
                min-width: 100px !important;
                font-style: italic !important;
                color: #000 !important;
            }

            .info-separator {
                margin: 0 5px !important;
                color: #000 !important;
            }

            .info-value {
                color: #000 !important;
                font-weight: bold !important;
            }

            .report-content {
                margin-top: 30px !important;
                margin-bottom: 15px !important;
                font-size: 14px !important;
            }

            .report-content div {
                font-size: 14px !important;
                line-height: 1.4em !important;
                margin: 0 !important;
                padding: 15px 0 !important;
            }

            .signature {
                font-size: 13px !important;
                line-height: 1.2em !important;
                margin-top: 15px !important;
            }
        }
    </style>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Print functionality
            document.getElementById('printBtn').addEventListener('click', function() {
                window.print();
            });

            // Download .doc functionality
            document.getElementById('downloadDocBtn').addEventListener('click', function() {
                // Clone the content to avoid modifying the original
                let contentElement = document.getElementById('printableContent').cloneNode(true);

                // Hide the screen/print patient info section and show the doc table
                const screenPrintSection = contentElement.querySelector('.screen-print-only');
                const docTable = contentElement.querySelector('.doc-only');

                if (screenPrintSection) {
                    screenPrintSection.style.display = 'none';
                }
                if (docTable) {
                    docTable.style.display = 'block';
                }

                // Get the content from the modified clone
                let content = contentElement.innerHTML;

                // Process .cus-linebreak elements to ensure line breaks work in Word
                content = content.replace(/(<[^>]*class="[^"]*cus-linebreak[^"]*"[^>]*>)([\s\S]*?)(<\/[^>]+>)/g, function(match, openTag, innerContent, closeTag) {
                    // Convert newlines to <br> tags within cus-linebreak elements
                    const processedContent = innerContent.replace(/\n/g, '<br>').replace(/\r/g, '');
                    return openTag + processedContent + closeTag;
                });

                // Create HTML structure for Word document
                const htmlContent = `
                    <!DOCTYPE html>
                    <html>
                    <head>
                        <meta charset="utf-8">
                        <title>Report - {{ $report->patient_name }}</title>
                        <style>
                            body { font-family: Arial, sans-serif; margin: 0px; }
                            .text-center { text-align: center; }
                            h5 {font-size: 14px; text-align: center;}
                            .text-end { text-align: right; }
                            .fw-bold { font-weight: bold; }
                            .text-muted { color: #6c757d; }
                            .small { font-size: 1em; }
                            .mb-0 { margin-bottom: 0; }
                            .mb-1 { margin-bottom: 0.25rem; }
                            .mb-2 { margin-bottom: 0.5rem; }
                            .mb-3 { margin-bottom: 1rem; }
                            .mb-4 { margin-bottom: 1.5rem; }
                            .mt-4 { margin-top: 1.5rem; }
                            .border-bottom { border-bottom: 1px solid #dee2e6; }
                            .pb-2 { padding-bottom: 0.5rem; }
                            .pt-3 { padding-top: 1rem; }
                            .p-3 { padding: 1rem; }
                            .border { border: 1px solid #000; }
                            .border-2 { border-width: 2px; }
                            .border-dark { border-color: #000; }
                            .rounded { border-radius: 0.25rem; }
                            .header-box { border: 2px solid #000; padding: 15px; margin: 35px 18px 5px 18px; text-align: center; }
                            .header-box h2 { font-size: 18px; margin: 0; font-weight: bold; }
                            .report-content { margin-top: 50px; }
                            .cus-linebreak { margin-top: 0; padding-top: 0; }
                            .screen-print-only { display: none; }
                            .doc-only { display: block; }
                            .cus-linebreak {
                                white-space: pre-wrap;
                                line-height: 1.6;
                                word-wrap: break-word;
                            }
                            .cus-linebreak br {
                                display: block;
                                margin: 0.5em 0;
                                content: "";
                            }
                        </style>
                    </head>
                    <body>
                        ${content}
                    </body>
                    </html>
                `;

                // Create blob and download
                const blob = new Blob([htmlContent], { type: 'application/msword' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = 'Report_{{ $report->patient_name }}_{{ $report->patient_id }}.doc';
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
            });
        });
    </script>

    <!-- Print Help Modal -->
    <div class="modal fade" id="printHelpModal" tabindex="-1" aria-labelledby="printHelpModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="printHelpModalLabel">
                        <i class="bi bi-info-circle me-2"></i>Print Settings Help
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-info">
                        <strong>Important:</strong> To get a clean printout without browser headers and footers, please turn off "Headers and footers" in your browser's print settings.
                    </div>

                    <div class="row">
                        <!-- Chrome Instructions -->
                        <div class="col-md-12 mb-5">
                            <h6 class="fw-bold text-primary">
                                <i class="bi bi-browser-chrome me-1"></i>Google Chrome
                            </h6>
                            <ol class="small">
                                <li>Click the <kbd>Print</kbd> button</li>
                                <li>Click on "More settings"</li>
                                <li>Uncheck "Headers and footers"</li>
                                <li>Click "Print"</li>
                            </ol>
                            <div class="text-center mt-2">
                                <img src="{{ asset('assets/img/print_chrome.png') }}" alt="Chrome Print Settings" class="img-fluid border rounded" style="max-height: auto;">
                            </div>
                        </div>

                        <!-- Firefox Instructions -->
                        <div class="col-md-12 mb-5">
                            <h6 class="fw-bold text-warning">
                                <i class="bi bi-browser-firefox me-1"></i>Mozilla Firefox
                            </h6>
                            <ol class="small">
                                <li>Click the <kbd>Print</kbd> button</li>
                                <li>Click on "More/Fewer settings"</li>
                                <li>Uncheck "Print headers and footers"</li>
                                <li>Click "Print"</li>
                            </ol>
                            <div class="text-center mt-2">
                                <img src="{{ asset('assets/img/print_firefox.png') }}" alt="Firefox Print Settings" class="img-fluid border rounded" style="max-height: auto;">
                            </div>
                        </div>
                    </div>

                    {{-- <div class="mt-3">
                        <h6 class="fw-bold">Additional Tips:</h6>
                        <ul class="small">
                            <li>Set margins to "Minimum" or "None" for best results</li>
                            <li>Choose "A4" paper size if available</li>
                            <li>Select "Portrait" orientation</li>
                            <li>Ensure "Print backgrounds" is enabled for borders and styling</li>
                        </ul>
                    </div> --}}
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>
@endsection
