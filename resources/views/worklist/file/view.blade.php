@extends('master')

@section('title')
    Report View
@endsection

@section('header')

@endsection

@section('page_id')
    page-add-job-report-view
@endsection

@section('page_content')

    <!-- Main Content -->
    <div class="container-fluid py-4">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <h1 class="h3 mb-0 text-gray-800">
                        <i class="bi bi-file-text me-2"></i>Write Report
                    </h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb mb-0">
                            <li class="breadcrumb-item"><a href="{{ url('/worklist') }}">Worklist</a></li>
                            <li class="breadcrumb-item active" aria-current="page">Write Report</li>
                        </ol>
                    </nav>
                </div>
            </div>
        </div>

        <!-- Image Gallery -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card shadow-sm">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-success">
                            <i class="bi bi-images me-2"></i>Medical Images
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">

                            @foreach ($report->file_links as $key => $image)
                                <div class="col-md-4">
                                    <div class="card border-0 shadow-sm">
                                        <img src="{{ config('filesystems.base_url') . $image }}" class="card-img-top medical-image" alt="Medical Image 1" style="cursor: pointer;" data-image="{{ config('filesystems.base_url') . $image }}">
                                        {{-- <div class="card-body p-2">
                                            <small class="text-muted">Image {{ $key + 1 }}</small>
                                        </div> --}}
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            </div>
        </div>

        @if(auth()->user()->type == \App\Constants\UserType::RADIOLOGIST)
        <!-- Report Writing Form -->
        <div class="row">
            <div class="col-12">
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <h6 class="m-0 font-weight-bold text-primary">
                                    <i class="bi bi-file-text me-2"></i>Radiology Report
                                </h6>
                            </div>

                            <div class="col-md-6 text-end">
                                <div class="d-flex gap-2 justify-content-end">
                                    <button type="button" class="btn btn-sm btn-outline-secondary" onclick="showSaveTemplateModalDialog()">
                                        <i class="bi bi-save me-1"></i>Save as Template
                                    </button>
                                </div>
                            </div>

                        </div>
                    </div>
                    <div class="card-body">
                        <form id="reportForm">

                            <!-- Report Template Selection -->
                            <div class="row mb-3">
                                <div class="col-md-12">
                                    <label for="reportTemplate" class="form-label fw-bold">Report Template</label>
                                    <select class="form-select" id="reportTemplate" name="template">
                                        <option value="">Select Saved Template (Optional)</option>
                                        @foreach ($feedback_templates as $template)
                                            <option value="{{ $template->description }}">{{ $template->name }}</option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>

                            <!-- Report Content -->
                            <div class="mb-3">
                                <label for="reportContent" class="form-label fw-bold">Report Content</label>
                                <textarea @if($report->status == \App\Constants\LabReportStatus::COMPLETED) readonly @endif class="form-control" id="reportContent" name="report_content" rows="15"
                                          placeholder="Enter your radiology report here..." required>{{ $report->report_feedback }}</textarea>
                                <div class="form-text">Use standard radiology reporting format including Technique, Findings, and Impression sections.</div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
        @endif

        <!-- Patient Information -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card shadow-sm">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="bi bi-person me-2"></i>Patient Information
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <strong>Patient ID:</strong> {{ $report->patient_id }}
                            </div>
                            <div class="col-md-3">
                                <strong>Patient Name:</strong> {{ $report->patient_name }}
                            </div>
                            <div class="col-md-3">
                                <strong>Age:</strong> {{ $report->patient_age }}
                            </div>
                            <div class="col-md-3">
                                <strong>Gender:</strong> {{ $report->patient_gender }}
                            </div>
                        </div>
                        <div class="row mt-2">
                            <div class="col-md-3">
                                <strong>Procedure:</strong> {{ $report->procedure }}
                            </div>
                            <div class="col-md-3">
                                <strong>Clinical History:</strong> {{ $report->clinical_history == '0' ? 'N/A' : $report->clinical_history }}
                            </div>
                            <div class="col-md-3">
                                <strong>Modality:</strong> {{ $report->modality }}
                            </div>
                            {{-- <div class="col-md-3">
                                <strong>Hospital:</strong> {{ $report->tenant->name }}
                            </div>
                            <div class="col-md-3">
                                <strong>Referred by (Doctor Name):</strong> {{ $report->doctor?->name }}
                            </div> --}}
                            <div class="col-md-3">
                                <strong>Study Date:</strong> {{ date('d M Y', strtotime($report->study_time)) }}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        @if(auth()->user()->type == \App\Constants\UserType::RADIOLOGIST)
        <!-- Action Buttons -->
        <div class="row">
            <div class="col-12">
                <div class="card shadow-sm">
                    <div class="card-body">
                        <div class="d-flex gap-2 justify-content-start">
                            <button type="button" class="btn btn-outline-secondary" onclick="window.history.back()">
                                <i class="bi bi-x-circle me-1"></i>Cancel
                            </button>
                            <button type="button" class="btn btn-success" id="submitReport" @if($report->status == \App\Constants\LabReportStatus::COMPLETED) disabled @endif>
                                <i class="bi bi-check-circle me-1"></i>@if($report->status == \App\Constants\LabReportStatus::COMPLETED) Already Submitted @else Submit Report @endif
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        @endif
    </div>

    <!-- Save Template Modal -->
    <div class="modal fade" id="SaveTemplateModal" tabindex="-1" aria-labelledby="SaveTemplateModal" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-save text-primary me-2"></i>Save Report Template
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label for="templateNameModal" class="form-label">Report Template Name <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="templateNameModal" name="templateName" placeholder="Enter template name" required>
                        <div class="form-text">Choose a descriptive name for this report template</div>
                    </div>
                    <div class="mt-3">
                        <small class="text-muted">
                            <i class="bi bi-info-circle me-1"></i>
                            This will save the current report content as a reusable template.
                        </small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="bi bi-x-circle me-1"></i>Cancel
                    </button>
                    <button type="button" class="btn btn-primary" id="confirmSaveTemplateModal">
                        <i class="bi bi-save me-1"></i>Save Template
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Image Modal -->
    <div class="modal fade" id="imageModal" tabindex="-1" aria-labelledby="imageModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="imageModalLabel">
                        <i class="bi bi-zoom-in me-2"></i>Medical Image - Large View
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body text-center">
                    <img id="modalImage" src="" class="img-fluid" alt="Large Medical Image" style="max-height: 80vh;">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="bi bi-x-circle me-1"></i>Close
                    </button>
                </div>
            </div>
        </div>
    </div>

@endsection

@section('footer')

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('reportTemplate').addEventListener('change', function() {
                const reportContent = document.getElementById('reportContent');
                reportContent.value = this.value;
            });
        });
    </script>

    <script>
        function showSaveTemplateModalDialog() {
            var modal = new bootstrap.Modal(document.getElementById('SaveTemplateModal'));
            modal.show();

            document.getElementById('confirmSaveTemplateModal').addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                var templateName = document.getElementById('templateNameModal').value;
                var templateContent = document.getElementById('reportContent').value;

                if (!templateName) {
                    toastr.error('Please enter a template name');
                    return;
                }

                if (!templateContent) {
                    toastr.error('Please write some report content before saving as template');
                    return;
                }

                modal.hide();
                $.ajax({
                    url: '{{ url('/worklist/save-feedback-template') }}',
                    type: 'POST',
                    data: {
                        template_name: templateName,
                        description: templateContent,
                        _token: '{{ csrf_token() }}'
                    },
                    success: function(response) {
                        if (response.success) {
                            Swal.fire(
                                'Done!',
                                'Report template has been saved successfully.',
                                'success'
                            ).then(() => {
                                location.reload();
                            });
                        } else {
                            toastr.error(response.message);
                        }
                    },
                    error: function(xhr) {
                        toastr.error(xhr.responseJSON?.message || 'Something went wrong');
                    }
                });
            });
        }
    </script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Image modal functionality
            const imageModal = document.getElementById('imageModal');
            const modalImage = document.getElementById('modalImage');

            document.querySelectorAll('.medical-image').forEach(img => {
                img.addEventListener('click', function() {
                    const imageSrc = this.getAttribute('data-image');
                    modalImage.src = imageSrc;

                    // Manually show modal if Bootstrap data attributes don't work
                    const modal = new bootstrap.Modal(imageModal);
                    modal.show();
                });
            });
        });
    </script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            $('#submitReport').on('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                const reportContent = document.getElementById('reportContent').value;

                if (!reportContent.trim()) {
                    toastr.error('Report content cannot be empty');
                    return;
                }

                // Show loading state
                this.innerHTML = '<i class="bi bi-hourglass-split me-1"></i>Submitting...';
                this.disabled = true;

                // Send AJAX request
                $.ajax({
                    url: '{{ url('worklist/'.$report->id.'/save-feedback') }}',
                    type: 'POST',
                    data: {
                        _token: '{{ csrf_token() }}',
                        report_content: reportContent
                    },
                    success: function(response) {
                        if (response.success) {
                            toastr.success(response.message);
                            setTimeout(function() {
                                window.location.href = '/worklist';
                            }, 1500);
                        } else {
                            toastr.error('An error occurred while saving report feedback');

                            const submitBtn = document.getElementById('submitReport');
                            submitBtn.innerHTML = '<i class="bi bi-x-circle me-1"></i> Submit Report';
                            submitBtn.disabled = false;
                            document.getElementById('reportContent').readOnly = false;
                        }
                    },
                    error: function() {
                        toastr.error('An error occurred while saving report feedback');
                        const submitBtn = document.getElementById('submitReport');
                        submitBtn.innerHTML = '<i class="bi bi-x-circle me-1"></i> Submit Report';
                        submitBtn.disabled = false;
                        document.getElementById('reportContent').readOnly = false;
                    },
                    complete: function() {

                    }
                });
            });
        });
    </script>
@endsection
