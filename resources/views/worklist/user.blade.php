@extends('master')

@section('title')
    Worklist
@endsection

@section('header')

@endsection

@section('page_id')
    page-user-worklist
@endsection

@section('page_content')

    <div class="container-fluid py-4">

        <!-- Search Filter Block -->
        <div class="row mb-4">
            <div class="col-12">
                <form action="{{ url('worklist') }}" method="GET">
                <div class="card shadow-sm filter-block">
                    <div class="card-body py-3">
                        <div class="row align-items-center">
                            <div class="col-auto">
                                <h6 class="mb-0 text-muted">
                                    <i class="bi bi-funnel me-2"></i>Filters:
                                </h6>
                            </div>

                            <div class="col-md-3 col-sm-6">
                                <div class="form-group">
                                    <label for="tenantFilter" class="form-label small text-muted mb-1">Hospital</label>
                                    <select class="form-select" id="tenantFilter" name="tenant">
                                        <option value="">All Hospitals</option>

                                        @foreach($tenants as $tenant)
                                        <option value="{{ $tenant->id }}" @if($tenant->id == request('tenant')) selected @endif>{{ $tenant->name }}</option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2 col-sm-6">
                                <div class="form-group">
                                    <label for="radiologistFilter" class="form-label small text-muted mb-1">Radiologist</label>
                                    <select class="form-select" id="radiologistFilter" name="radiologist">
                                        <option value="">All Radiologists</option>
                                        @foreach($radiologists as $radiologist)
                                            <option value="{{ $radiologist->id }}" @if($radiologist->id == request('radiologist')) selected @endif>{{ $radiologist->name }} {{ $radiologist->is_internal ? ' (Internal)' : '' }}</option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2 col-sm-6">
                                <div class="form-group">
                                    <label for="patientIdFilter" class="form-label small text-muted mb-1">Patient ID</label>
                                    <input type="text" class="form-control" id="patientIdFilter" name="patient_id" value="{{ request('patient_id') }}" placeholder="Enter Patient ID">
                                </div>
                            </div>
                            <div class="col-md-2 col-sm-6">
                                <div class="form-group">
                                    <label class="form-label small text-muted mb-1">&nbsp;</label>
                                    <button type="submit" class="btn btn-primary w-100" id="applyFilters">
                                        <i class="bi bi-search me-1"></i>Search
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                </form>
            </div>
        </div>

        <!-- Worklist Items -->
        <div class="row">
            <div class="col-12">
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <div class="row align-items-center">
                            <div class="col-md-3">
                                <h6 class="m-0 font-weight-bold text-primary">
                                    <i class="bi bi-list-task me-2"></i>Worklist Items
                                </h6>
                            </div>
                            <div class="col-md-6">
                                <div class="col-md-12" id="assignRadiologistBlock" style="display: none;">
                                    <div class="input-group" style="flex-wrap: inherit !important;">
                                        <select class="form-select form-select-sm" id="radiologistSelect" multiple>
                                            @foreach($radiologists as $radiologist)
                                                <option value="{{ $radiologist->id }}">{{ $radiologist->name }} {{ $radiologist->is_internal ? ' (Internal)' : '' }}</option>
                                            @endforeach
                                        </select>
                                        <button class="btn btn-sm btn-primary" type="button" id="assignRadiologistBtn">
                                            <i class="bi bi-person-check me-1"></i>Assign
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 text-end">
                                <div class="d-flex gap-2 justify-content-end">
                                    <a href="{{ url('/worklist') }}" class="btn btn-sm btn-outline-primary">
                                        <i class="bi bi-arrow-clockwise me-1"></i>Refresh
                                    </a>
                                    <button class="btn btn-sm btn-outline-success" id="mergeBtn" disabled>
                                        <i class="bi bi-archive me-1"></i>Merge
                                    </button>
                                    <button class="btn btn-sm btn-outline-danger" id="archiveBtn" disabled>
                                        <i class="bi bi-archive me-1"></i>Archive
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover table-striped default-table" id="worklistTable">
                                <thead class="table-dark">
                                <tr>
                                    <th width="40">
                                        <input type="checkbox" class="form-check-input" id="selectAll" title="Select All">
                                    </th>
                                    <th width="60">SL</th>
                                    <th width="60">View</th>
                                    <th>Print</th>
                                    <th>Status</th>
                                    <th>Patient ID</th>
                                    <th>Patient Name</th>
                                    <th>Procedure</th>
                                    <th>Clinical History</th>
                                    <th>Arrival Date/Time</th>
                                    <th>Hospital</th>
                                    <th>Modality</th>
                                    <th>Radiologist</th>
                                    <th>Order Time</th>
                                </tr>
                                </thead>
                                <tbody>

                                @forelse($worklist as $item)
                                    <tr>
                                        <td>
                                            <input type="checkbox" class="form-check-input row-checkbox"
                                                   value="{{ $item->id }}"
                                                   data-status="{{ $item->status }}"
                                                   data-parent_procedure="{{ $item->parent_procedure ?? '' }}"
                                                   data-patient="{{ $item->patient_id }}"
                                                   data-type="{{ $item->file_type }}"
                                            >
                                        </td>
                                        {{-- <td>{{ $loop->iteration + $worklist->perPage() * ($worklist->currentPage() - 1) }}</td> --}}
                                        <td>{{ $worklist->total() - ($loop->iteration + $worklist->perPage() * ($worklist->currentPage() - 1)) + 1 }}</td>
                                        <td>
                                            <div class="d-flex gap-1">
                                                @php
                                                    $file_links = json_decode($item->file_links, true);
                                                    if(json_last_error() === JSON_ERROR_NONE && is_array($file_links)){
                                                        $first_file = $file_links[0];
                                                        $multifile = 'yes';
                                                    }else{
                                                        $first_file = $item->file_links;
                                                        $multifile = 'no';
                                                    }
                                                @endphp

                                                @if($item->file_type == 'DICOM')
                                                    <a href="{{ url('worklist/'.$item->id).'/dcm-view?file=' . base64_encode(config('filesystems.base_url') . $first_file) . '&files=' . base64_encode($item->file_links) . '&multifile=' . $multifile . '&item=' . $item->id }}" class="btn btn-sm btn-dark x-ray-btn" style="background-color: #0a52b8;" title="View DICOM">
                                                        <img src="{{ asset('assets/img/ico-xray-dicom.svg') }}" alt="DICOM" style="width: 24px; height: 24px;">
                                                    </a>
                                                @else
                                                    <a href="{{ url('worklist/'.$item->id).'/dcm-view?file=' . base64_encode(config('filesystems.base_url') . $first_file) . '&files=' . base64_encode($item->file_links) . '&multifile=' . $multifile . '&item=' . $item->id }}" class="btn btn-sm btn-dark x-ray-btn" style="background-color: #333;" title="View Image">
                                                        <img src="{{ asset('assets/img/ico-xray-img.svg') }}" alt="Image" style="width: 24px; height: 24px;">
                                                    </a>
                                                @endif
                                            </div>
                                        </td>
                                        <td>
                                            @if($item->status == \App\Constants\LabReportStatus::COMPLETED)
                                                <a href="{{ url('worklist/'.$item->id.'/print') }}" class="btn btn-sm btn-outline-success bg-success text-white" title="Write Report with Images">
                                                    <i class="bi bi-printer"></i>
                                                </a>
                                            @else
                                                <button class="btn btn-sm btn-outline-secondary" disabled title="Print Report (Not Available)">
                                                    <i class="bi bi-printer"></i>
                                                </button>
                                            @endif
                                        </td>
                                        <td>
                                            @if($item->status == 'PENDING')
                                                <span class="badge bg-warning">Pending</span>
                                            @elseif($item->status == 'UNASSIGNED')
                                                <span class="badge bg-secondary">Unassigned</span>
                                            @elseif($item->status == 'ASSIGNED')
                                                <span class="badge bg-primary">Read in Progress</span>
                                            @elseif($item->status == 'COMPLETED')
                                                <span class="badge bg-success">Completed</span>
                                            @endif
                                        </td>
                                        <td><strong>{{ $item->patient_id }}</strong></td>
                                        <td>{{ $item->patient_name }}</td>
                                        <td class="editable-procedure-cell" data-id="{{ $item->id }}">
                                            <div class="view-mode d-flex align-items-center">
                                                <span class="procedure-text">{{ $item->procedure == '0' ? '' : ($item->procedure ?? '-') }}</span>
                                                <button type="button" class="btn btn-sm btn-link p-0 ms-1 edit-procedure" title="Edit Procedure">
                                                    <i class="bi bi-pencil text-primary"></i>
                                                </button>
                                            </div>
                                            <div class="edit-mode d-none">
                                                <div class="input-group input-group-sm">
                                                    <select class="form-control select2 procedure-select" style="width: 200px;">
                                                        <option value="">Select a procedure</option>
                                                        @foreach($procedures as $procedure)
                                                            <option value="{{ $procedure->id }}" {{ $item->procedure_id == $procedure->id ? 'selected' : '' }}>
                                                                {{ $procedure->name }}
                                                            </option>
                                                        @endforeach
                                                    </select>
                                                    <button class="btn btn-outline-success btn-sm save-procedure ms-1" type="button">
                                                        <i class="bi bi-check"></i>
                                                    </button>
                                                    <button class="btn btn-outline-secondary btn-sm cancel-edit" type="button">
                                                        <i class="bi bi-x"></i>
                                                    </button>
                                                </div>
                                            </div>
                                            <hr style="margin: 0;">
                                            @php
                                                $procedure_history = $item->procedure_history;
                                                if ($procedure_history == '@') {
                                                    $procedure_history = '';
                                                } elseif ($procedure_history) {
                                                    // Remove "0;" patterns from the string
                                                    $cleaned = preg_replace('/0;\s*/', '', $procedure_history);
                                                    // If after cleaning we only have whitespace/semicolons, show N/A
                                                    $procedure_history = preg_match('/^[;\s]*$/', $cleaned) ? 'N/A' : $cleaned;
                                                } else {
                                                    $procedure_history = '-';
                                                }
                                            @endphp
                                            <span id="procedure_history_{{ $item->id }}">{{ $procedure_history }}</span>
                                        </td>
                                        <td class="editable-clinical-history-cell" data-id="{{ $item->id }}">
                                            <div class="view-mode d-flex align-items-center">
                                                @php
                                                    $clinical_history = $item->clinical_history;
                                                    if ($clinical_history) {
                                                        // Remove "0;" patterns from the string
                                                        $cleaned = preg_replace('/0;\s*/', '', $clinical_history);
                                                        // If after cleaning we only have whitespace/semicolons, show N/A
                                                        $clinical_history = preg_match('/^[;\s]*$/', $cleaned) ? 'N/A' : $cleaned;
                                                    } else {
                                                        $clinical_history = 'N/A';
                                                    }
                                                @endphp
                                                <span class="clinical-history-text">{{ $clinical_history }}</span>
                                                <button type="button" class="btn btn-sm btn-link p-0 ms-1 edit-clinical-history" title="Edit Clinical History">
                                                    <i class="bi bi-pencil text-primary"></i>
                                                </button>
                                            </div>
                                            <div class="edit-mode d-none">
                                                <div class="input-group input-group-sm">
                                                    <input type="text" class="form-control clinical-history-input" value="{{ $item->clinical_history ?? '' }}">
                                                    <button class="btn btn-outline-success btn-sm save-clinical-history" type="button">
                                                        <i class="bi bi-check"></i>
                                                    </button>
                                                    <button class="btn btn-outline-secondary btn-sm cancel-edit" type="button">
                                                        <i class="bi bi-x"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </td>
                                        <td>{{ date('d M Y h:i A', strtotime($item->study_time)) }}</td>
                                        <td>{{ $item->tenant->name }}</td>
                                        <td>{{ $item->modality }}</td>
                                        <td>
                                            @if($item->status == \App\Constants\LabReportStatus::COMPLETED)
                                                <i class="bi bi-person-check me-1"></i>{{ $item->completedBy->name }}
                                            @else
                                                @foreach($item->assignees as $key => $assignee)
                                                    @if($key > 0) <hr style="margin: 0; padding: 0">@endif
                                                    <i class="bi bi-person-check me-1"></i>{{ $assignee->name }} {{ $assignee->is_internal ? ' (Internal)' : '' }}
                                                @endforeach
                                            @endif
                                        </td>
                                        <td>{{ $item->assigned_at ? date('d M Y h:i A', strtotime($item->assigned_at)) : '-' }}</td>
                                    </tr>
                                    @empty
                                        <tr>
                                            <td colspan="14" class="text-center">No Worklist Found</td>
                                        </tr>
                                @endforelse

                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <div class="d-flex justify-content-center">
                            {{ $worklist->appends(request()->query())->links() }}
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>


    <!-- Error Messages Modal -->
    <div class="modal fade" id="errorMessagesModal" tabindex="-1" aria-labelledby="errorMessagesModalLabel" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false">
        <div class="modal-dialog modal-lg modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title" id="errorMessagesModalLabel">Assignment Errors</h5>
                    <!-- Remove the close (X) button -->
                </div>
                <div class="modal-body" id="errorMessagesBody">
                    <!-- Error content will be inserted here -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-danger" id="acknowledgeButton">Acknowledge</button>
                </div>
            </div>
        </div>
    </div>

@endsection

@section('footer')

    <script>
        $(document).ready(function() {
            // Initialize select2 for multi-select
            $('#radiologistSelect').select2({
                placeholder: 'Select Radiologist(s)',
                width: '100%',
                dropdownParent: $('#assignRadiologistBlock')
            });
        });
    </script>

    <script>
        //select worklist
        $(document).ready(function() {

            // Toggle radiologist block based on checkbox selection
            function toggleRadiologistBlock() {
                const checkedCount = $('.row-checkbox:checked').length;
                if (checkedCount > 0) {
                    $('#assignRadiologistBlock').show();
                    $('#archiveBtn').prop('disabled', false);
                } else {
                    $('#assignRadiologistBlock').hide();
                    $('#archiveBtn').prop('disabled', true);
                }

                if (checkedCount > 1) {
                    $('#mergeBtn').prop('disabled', false);
                } else {
                    $('#mergeBtn').prop('disabled', true);
                }
            }

            // Select/Deselect all checkboxes
            $('#selectAll').change(function() {
                $('.row-checkbox').prop('checked', $(this).prop('checked'));
                toggleRadiologistBlock();
            });

            // Handle individual checkbox change
            $(document).on('change', '.row-checkbox', function() {
                const allChecked = $('.row-checkbox:not(:checked)').length === 0;
                $('#selectAll').prop('checked', allChecked);
                toggleRadiologistBlock();
            });
        });
    </script>
    <script>
        $(document).ready(function() {
            $('#archiveBtn').click(function() {
                const selectedCompletedItems = [];
                $('.row-checkbox:checked').each(function () {
                    if($(this).data('status') === 'COMPLETED'){
                        selectedCompletedItems.push($(this).val());
                    }
                });

                if (selectedCompletedItems.length === 0) {
                    toastr.error('Please select at least one completed work item');
                    return;
                }

                Swal.fire({
                    title: 'Are you sure?',
                    text: `Archive ${selectedCompletedItems.length} completed work item(s)?`,
                    icon: 'question',
                    showCancelButton: true,
                    confirmButtonText: 'Yes, Archive',
                    cancelButtonText: 'Cancel'
                }).then((result) => {
                    if (result.isConfirmed) {
                        $.ajax({
                            url: '{{ route("worklist.bulk-archive") }}',
                            type: 'POST',
                            data: {
                                _token: '{{ csrf_token() }}',
                                worklist_ids: selectedCompletedItems,
                            },
                            success: function(response) {
                                if (response.success) {
                                    toastr.success('Work items successfully archived');
                                    location.reload(true);
                                } else {
                                    toastr.error(response.message || 'Failed to archive work items');
                                }
                            },
                            error: function(xhr) {
                                toastr.error(xhr.responseJSON?.message || 'An error occurred');
                            }
                        });
                    }
                });

            });
        });
    </script>
    <script>
        $(document).ready(function() {

            // Handle assign radiologist button click
            $('#assignRadiologistBtn').click(function() {
                const selectedItems = [];
                var error = false;
                $('.row-checkbox:checked').each(function() {
                    if($(this).attr('data-parent_procedure') !== '' || $(this).attr('data-type') !== 'DICOM'){
                        selectedItems.push($(this).val());
                    }else{
                        toastr.error('Please update the procedure first!');
                        error = true;
                        return false;
                    }
                });
                if(error){
                    return;
                }

                const radiologists = $('#radiologistSelect').val();

                if (!radiologists || radiologists.length === 0) {
                    toastr.error('Please select at least one radiologist');
                    return;
                }

                Swal.fire({
                    title: 'Are you sure?',
                    text: `Assign ${selectedItems.length} work item(s) to selected radiologist(s)?`,
                    icon: 'question',
                    showCancelButton: true,
                    confirmButtonText: 'Yes, Assign',
                    cancelButtonText: 'Cancel'
                }).then((result) => {
                    if (result.isConfirmed) {
                        $.ajax({
                            url: '{{ route("worklist.bulk-assign") }}',
                            type: 'POST',
                            data: {
                                _token: '{{ csrf_token() }}',
                                worklist_ids: selectedItems,
                                radiologist_ids: radiologists
                            },
                            success: function(response) {
                                if (response.success) {
                                    if(response.total_worklist_count === response.success_worklist_count){
                                        toastr.success('Work items successfully assigned');
                                        location.reload(true);
                                    }else if(response.success_worklist_count > 0){
                                        toastr.success(`${response.success_worklist_count} work items successfully assigned out of ${response.total_worklist_count}`);
                                        location.reload(true);
                                    }else{
                                        toastr.error('Failed to assign work items. Check the dialog for details.');
                                    }
                                    if(response.selection_error_messages !== ''){
                                        if (response.selection_error_messages !== '') {
                                            $('#errorMessagesBody').html(response.selection_error_messages.replace(/\n/g, '<br>'));
                                            const errorModal = new bootstrap.Modal(document.getElementById('errorMessagesModal'), {
                                                backdrop: 'static',
                                                keyboard: false
                                            });
                                            errorModal.show();

                                            // Handle Acknowledge button
                                            $('#acknowledgeButton').off('click').on('click', function () {
                                                errorModal.hide();
                                            });
                                        }
                                    }
                                } else {
                                    toastr.error(response.message || 'Failed to assign work items');
                                }
                            },
                            error: function(xhr) {
                                toastr.error(xhr.responseJSON?.message || 'An error occurred');
                            }
                        });
                    }
                });
            });
        });
    </script>

    <script>
        $(document).ready(function() {

            // Handle assign radiologist button click
            $('#mergeBtn').click(function() {
                const selectedItems = [];
                const patientIds = [];
                var error = false;
                $('.row-checkbox:checked').each(function() {
                    if($(this).data('type') !== 'DICOM') {
                        toastr.error('All work items should be DICOM');
                        error = true;
                        return false;
                    }
                    if($(this).data('status') !== 'UNASSIGNED') {
                        toastr.error('All work items should be unassigned');
                        error = true;
                        return false;
                    }
                    selectedItems.push($(this).val());
                    patientIds.push($(this).data('patient'));
                });
                if(error){
                    return;
                }

                //all patients should be same
                patientIds.every(id => id === patientIds[0]);
                if(patientIds.length  < 2){
                    toastr.error('Please select at least two work item');
                    return;
                }
                if(patientIds.every(id => id === patientIds[0]) === false){
                    toastr.error('Please select from same patient ID!');
                    return;
                }

                Swal.fire({
                    title: 'Are you sure?',
                    text: 'Merge selected work items?',
                    icon: 'question',
                    showCancelButton: true,
                    confirmButtonText: 'Yes, Merge',
                    cancelButtonText: 'Cancel'
                }).then((result) => {
                    if (result.isConfirmed) {
                        $.ajax({
                            url: '{{ route("worklist.merge") }}',
                            type: 'POST',
                            data: {
                                _token: '{{ csrf_token() }}',
                                worklist_ids: selectedItems,
                            },
                            success: function(response) {
                                if (response.success) {
                                    toastr.success('Work items successfully merged');
                                    location.reload(true);
                                } else {
                                    toastr.error(response.message || 'Failed to merge work items');
                                }
                            },
                            error: function(xhr) {
                                toastr.error(xhr.responseJSON?.message || 'An error occurred');
                            }
                        });
                    }
                });
            });
        });
    </script>

    <style>
        .select2-container--default .select2-selection--multiple {
            border: 1px solid #ced4da;
            border-radius: 0.25rem;
            min-height: 38px;
        }
        .select2-container--default.select2-container--focus .select2-selection--multiple {
            border-color: #86b7fe;
            box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
        }
    </style>

    <script>
        $(document).ready(function() {
            // Toggle edit mode
            $(document).on('click', '.edit-clinical-history', function() {
                const cell = $(this).closest('.editable-clinical-history-cell');
                cell.find('.view-mode').addClass('d-none');
                cell.find('.edit-mode').removeClass('d-none');
                cell.find('.clinical-history-input').focus();
            });

            // Cancel edit
            $(document).on('click', '.cancel-edit', function() {
                const cell = $(this).closest('.editable-clinical-history-cell');
                cell.find('.edit-mode').addClass('d-none');
                cell.find('.view-mode').removeClass('d-none');
            });

            // Save clinical history
            $(document).on('click', '.save-clinical-history', function() {
                const cell = $(this).closest('.editable-clinical-history-cell');
                const id = cell.data('id');
                const newValue = cell.find('.clinical-history-input').val();

                // Show loading state
                const saveBtn = cell.find('.save-clinical-history');
                saveBtn.prop('disabled', true).html('<i class="bi bi-arrow-repeat spinner-border spinner-border-sm"></i>');

                // Send AJAX request
                $.ajax({
                    url: '{{ url("worklist/update-clinical-history") }}',
                    type: 'POST',
                    data: {
                        _token: '{{ csrf_token() }}',
                        id: id,
                        clinical_history: newValue
                    },
                    success: function(response) {
                        if (response.success) {
                            // Clean the value before displaying
                            let cleanedValue = newValue;
                            if (cleanedValue) {
                                // Remove "0;" patterns from the string
                                cleanedValue = cleanedValue.replace(/0;\s*/g, '');
                                // If after cleaning we only have whitespace/semicolons, show N/A
                                cleanedValue = /^[;\s]*$/.test(cleanedValue) ? 'N/A' : cleanedValue;
                            } else {
                                cleanedValue = 'N/A';
                            }
                            // Update the displayed value
                            cell.find('.clinical-history-text').text(cleanedValue);
                            // Exit edit mode
                            cell.find('.edit-mode').addClass('d-none');
                            cell.find('.view-mode').removeClass('d-none');
                            // Show success message
                            toastr.success('Clinical history updated successfully');

                            if (response.is_status_updated) {
                                const statusCell = cell.closest('tr').find('.status-cell');
                                const badge = statusCell.find('span.badge');

                                badge.text('Unassigned');
                                badge.removeClass('bg-primary bg-warning bg-success bg-secondary').addClass('bg-secondary');
                            }
                        } else {
                            toastr.error('Failed to update clinical history');
                        }
                    },
                    error: function() {
                        toastr.error('An error occurred while updating clinical history');
                    },
                    complete: function() {
                        saveBtn.prop('disabled', false).html('<i class="bi bi-check"></i>');
                    }
                });
            });

            // Handle Enter key in input field
            $(document).on('keypress', '.clinical-history-input', function(e) {
                if (e.which === 13) { // Enter key
                    $(this).closest('.editable-clinical-history-cell').find('.save-clinical-history').click();
                    return false;
                }
            });
        });
    </script>

    <script>
        // Toggle edit mode for procedure
        $(document).on('click', '.edit-procedure', function() {
            const cell = $(this).closest('.editable-procedure-cell');
            cell.find('.view-mode').addClass('d-none');
            cell.find('.edit-mode').removeClass('d-none');

            // Initialize Select2 for this dropdown
            cell.find('.procedure-select').select2({
                theme: 'bootstrap-5',
                width: '100%',
                dropdownParent: cell.find('.edit-mode')
            });

            // Open the dropdown immediately
            cell.find('.procedure-select').select2('open');
        });

        // Save procedure
        $(document).on('click', '.save-procedure', function() {
            const cell = $(this).closest('.editable-procedure-cell');
            const id = cell.data('id');
            const procedureId = cell.find('.procedure-select').val();
            const procedureText = cell.find('.procedure-select option:selected').text().split(' (')[0]; // Get just the procedure name

            // Show loading state
            const saveBtn = cell.find('.save-procedure');
            saveBtn.prop('disabled', true).html('<i class="bi bi-arrow-repeat spinner-border spinner-border-sm"></i>');

            // Send AJAX request
            $.ajax({
                url: '{{ url("worklist/update-procedure") }}',
                type: 'POST',
                data: {
                    _token: '{{ csrf_token() }}',
                    id: id,
                    procedure_id: procedureId
                },
                success: function(response) {
                    if (response.success) {
                        // Update the displayed value
                        cell.find('.procedure-text').text(procedureText || '-');
                        // Exit edit mode
                        cell.find('.edit-mode').addClass('d-none');
                        cell.find('.view-mode').removeClass('d-none');
                        // Show success message
                        toastr.success('Procedure updated successfully');
                        // Update the checkbox's data-parent_procedure attribute
                        cell.closest('tr').find('.row-checkbox').attr('data-parent_procedure', 'OK');
                        // Clean the procedure history before displaying
                        let cleanedProcedureHistory = response.procedure_history;
                        if (cleanedProcedureHistory && cleanedProcedureHistory !== '@') {
                            // Remove "0;" patterns from the string
                            cleanedProcedureHistory = cleanedProcedureHistory.replace(/0;\s*/g, '');
                            // If after cleaning we only have whitespace/semicolons, show N/A
                            cleanedProcedureHistory = /^[;\s]*$/.test(cleanedProcedureHistory) ? 'N/A' : cleanedProcedureHistory;
                        } else {
                            cleanedProcedureHistory = cleanedProcedureHistory === '@' ? '' : '-';
                        }
                        $('#procedure_history_' + id).text(cleanedProcedureHistory);
                    } else {
                        toastr.error('Failed to update procedure');
                    }
                },
                error: function() {
                    toastr.error('An error occurred while updating procedure');
                },
                complete: function() {
                    saveBtn.prop('disabled', false).html('<i class="bi bi-check"></i>');
                }
            });
        });

        // Handle Enter key in procedure select
        $(document).on('keydown', '.select2-search__field', function(e) {
            if (e.which === 13) { // Enter key
                $(this).closest('.editable-procedure-cell').find('.save-procedure').click();
                return false;
            }
        });

        // Handle cancel edit for procedure
        $(document).on('click', '.cancel-edit', function() {
            const cell = $(this).closest('.editable-procedure-cell');
            cell.find('.edit-mode').addClass('d-none');
            cell.find('.view-mode').removeClass('d-none');
        });
    </script>
@endsection
