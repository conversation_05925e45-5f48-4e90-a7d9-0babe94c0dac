<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BMS Report Viewer</title>

    <!-- Legacy Cornerstone Dependencies (More Stable) -->
    <script src="https://unpkg.com/cornerstone-core@2.6.1/dist/cornerstone.min.js"></script>
    <script src="https://unpkg.com/cornerstone-math@0.1.10/dist/cornerstoneMath.min.js"></script>
    <script src="https://unpkg.com/cornerstone-tools@6.0.10/dist/cornerstoneTools.min.js"></script>
    <script src="https://unpkg.com/dicom-parser@1.8.21/dist/dicomParser.min.js"></script>
    <script src="https://unpkg.com/cornerstone-wado-image-loader@4.13.2/dist/cornerstoneWADOImageLoader.bundle.min.js"></script>
    <script src="https://unpkg.com/hammerjs@2.0.8/hammer.min.js"></script>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.6/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css">

    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Toastr CSS & JS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>

    <!-- Custom styles -->
    <link rel="stylesheet" href="{{ url()->asset('assets/css/dicom.css?v=2508003') }}">

    <!-- Modal fixes -->
    <script>
        toastr.options = {
            "closeButton": true,
            "progressBar": true,
            "positionClass": "toast-bottom-right",
            "timeOut": "5000",
            "extendedTimeOut": "2000"
        };
    </script>

    <style>
        .modal {
            z-index: 9999 !important;
        }
        .modal-backdrop {
            z-index: 9998 !important;
        }
        .modal-content {
            pointer-events: auto !important;
        }
        .modal-body {
            pointer-events: auto !important;
        }
        .modal-body * {
            pointer-events: auto !important;
        }
        /* Ensure form elements are selectable */
        .modal input, .modal textarea, .modal select, .modal button {
            pointer-events: auto !important;
            user-select: auto !important;
        }
        /* Override any DICOM viewer styles that might interfere */
        .modal .form-control, .modal .form-select, .modal .btn {
            pointer-events: auto !important;
            user-select: auto !important;
            -webkit-user-select: auto !important;
            -moz-user-select: auto !important;
            -ms-user-select: auto !important;
        }
        /* Ensure modal is above everything */
        .modal.show {
            display: block !important;
            z-index: 10000 !important;
        }

        /* Page Preloader */
        .page-preloader {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: #1a1a1a;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 99999;
            transition: opacity 0.3s ease-out;
        }

        .page-preloader.fade-out {
            opacity: 0;
            pointer-events: none;
        }

        .preloader-content {
            text-align: center;
            color: white;
        }

        .preloader-spinner {
            width: 50px;
            height: 50px;
            border: 4px solid #333;
            border-top: 4px solid #007bff;
            border-radius: 50%;
            animation: preloader-spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        .preloader-content p {
            margin: 0;
            font-size: 16px;
            color: #ccc;
        }

        @keyframes preloader-spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Hide metadata modal by default */
        #metadataModal {
            display: none !important;
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            width: 100% !important;
            height: 100% !important;
            margin: 0 !important;
            padding: 20px !important;
            box-sizing: border-box !important;
        }

        /* Center the metadata modal when visible */
        #metadataModal.show,
        #metadataModal[style*="block"] {
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
        }

        #metadataModal .modal-content {
            margin: 0 auto !important;
            max-width: 90% !important;
            max-height: 90% !important;
            width: auto !important;
            position: relative !important;
            transform: none !important;
            left: auto !important;
            right: auto !important;
        }

        /* File Link Button Styles */
        .file-link-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 12px 16px;
            margin-bottom: 8px;
            background: #ffffff;
            border: 1px solid #e0e6ed;
            border-radius: 8px;
            text-decoration: none;
            color: #374151;
            font-weight: 500;
            font-size: 12px;
            transition: all 0.2s ease;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .file-link-btn:hover {
            background: #f8fafc;
            border-color: #cbd5e1;
            color: #374151;
            text-decoration: none;
            transform: translateY(-1px);
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
        }

        .file-link-btn img {
            width: 32px;
            height: 32px;
            margin-right: 8px;
            opacity: 0.8;
        }

        .file-link-btn:hover img {
            opacity: 1;
        }

        .file-list-section {
            display: none !important;
        }
        .metadata-button-container {
            display: none !important;
        }

        /* Hide tooltips on mobile devices */
        @media (max-width: 768px) {
            .toolbar .tool-btn[title]:hover::after,
            .toolbar .tool-btn[title]:focus::after,
            .frame-btn[title]:hover::after,
            .frame-btn[title]:focus::after {
                display: none !important;
            }

            /* Remove pointer events for title attribute on mobile */
            .toolbar .tool-btn[title],
            .frame-btn[title] {
                position: relative;
            }

            .toolbar .tool-btn[title]::before,
            .frame-btn[title]::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                pointer-events: none;
            }
        }

        /* Completely disable tooltips on touch devices */
        @media (hover: none) and (pointer: coarse) {
            .toolbar .tool-btn[title],
            .frame-btn[title] {
                position: relative;
            }

            .toolbar .tool-btn[title]::after,
            .frame-btn[title]::after {
                display: none !important;
            }
        }



        /* Fix mobile scrolling issues */
        @media (max-width: 768px) {
            body {
                overflow: auto !important;
            }

            .app-container {
                height: auto !important;
                min-height: 100vh;
            }

            .main-content {
                overflow: visible !important;
                flex: none !important;
            }

            .viewer-area {
                overflow: visible !important;
                position: relative !important;
            }

            .viewer-container {
                overflow: visible !important;
                position: relative !important;
            }

            .sidebar {
                position: relative !important;
                overflow: visible !important;
                max-height: none !important;
                height: auto !important;
            }

            /* Fix canvas height on mobile */
            .cornerstone-canvas {
                height: auto !important;
                max-width: 100% !important;
                touch-action: none; /* Prevent default touch behaviors for pinch-to-zoom */
            }

            .viewport {
                min-height: 300px !important;
                touch-action: none; /* Prevent default touch behaviors */
            }
        }

        .sidebar-write-report {
            padding: 1.5rem;
            border-bottom: 1px solid var(--border-color);
        }
        .sidebar-write-report .btn {
            width: 100%;
        }

        /* File item container with download button */
        .file-item-container {
            position: relative;
            display: flex;
            align-items: center;
            margin-bottom: 8px;
        }

        .file-item-container .file-link-btn {
            flex: 1;
            margin-bottom: 0;
            margin-right: 8px;
        }

        .file-item-container.active .file-link-btn {
            margin-right: 8px;
        }

        .download-btn {
            background: #28a745;
            border: none;
            color: white;
            padding: 12px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            min-width: 44px;
            height: 44px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .download-btn:hover {
            background: #218838;
            transform: translateY(-1px);
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
        }

        .download-btn i {
            font-size: 16px;
        }
    </style>
</head>
<body>
<!-- Page Preloader -->
<div id="pagePreloader" class="page-preloader">
    <div class="preloader-content">
        <div class="preloader-spinner"></div>
        <p>Loading Report Viewer...</p>
    </div>
</div>

<div class="app-container">
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <h1 class="app-title">
                <i class="fas fa-x-ray"></i>
                Report Viewer
            </h1>
            <div class="header-controls">
                <a href="{{ url('/worklist') }}" class="btn btn-outline-secondary me-2" title="Back to Worklist">
                    <i class="fas fa-arrow-left me-1"></i>Back to Worklist
                </a>
                @if(auth()->user()->type == \App\Constants\UserType::RADIOLOGIST)
                    <button class="btn btn-primary me-2" id="writeReportBtn" title="Write Report">
                        <i class="fas fa-edit me-1"></i>Write Report
                    </button>
                @endif
                <button class="theme-toggle" id="themeToggle" title="Toggle theme">
                    <i class="fas fa-moon"></i>
                </button>
            </div>
        </div>
    </header>

    <!-- Main content -->
    <main class="main-content">
        <!-- Sidebar -->
        <aside class="sidebar">
            <!-- File List -->
            {{-- <div class="file-list-section">
                <h3><i class="fas fa-list"></i> Files</h3>
                <div class="file-list" id="fileList">
                    <div class="no-files">
                        <i class="fas fa-folder-open"></i>
                        <p>No files loaded</p>
                    </div>
                </div>
            </div> --}}

            <!-- File List Links -->
            @if(request('multifile') == 'yes')
                @php
                    $file_links = json_decode(base64_decode(request('files')), true);
                @endphp

            <div class="file-links-section" style="padding: 1.5rem;border-bottom: 1px solid var(--border-color);">
                <h3><i class="fas fa-list"></i> Files</h3>
                {{-- <div class="file-list"> --}}
                    <div class="row">

                        @foreach($file_links as $index => $file_link)
                            @php
                                $currentFileUrl = base64_encode(config('filesystems.base_url') . $file_link);
                                $requestedFile = request('file');
                                $isActive = $currentFileUrl === $requestedFile;

                                // Check if file is DICOM based on extension
                                $fileName = basename($file_link);
                                $isDicomFile = preg_match('/\.(dcm|dicom)$/i', $fileName) || $report->file_type == 'DICOM';
                            @endphp
                            <div class="file-item-container {{ $isActive ? 'active' : '' }}">
                                <a href="{{ url('worklist/'.request('item').'/dcm-view?file=' . base64_encode(config('filesystems.base_url') . $file_link) . '&files=' . request('files') . '&multifile=yes' . '&item=' . request('item')) }}"
                                   class="file-link-btn {{ $isActive ? 'active' : '' }}">
                                    <i class="fas fa-file-medical"></i>
                                    <span>Image {{ $index + 1 }}</span>
                                </a>
                                @if($isActive && !$isDicomFile)
                                    <button class="download-btn" onclick="downloadCurrentImage('{{ config('filesystems.base_url') . $file_link }}', 'Image_{{ $index + 1 }}')" title="Download Image">
                                        <i class="fas fa-download"></i>
                                    </button>
                                @endif
                            </div>
                        @endforeach

                    </div>
                {{-- </div> --}}
            </div>
            @endif

            <!-- Patient Info -->
            <div class="image-info-section">
                <h3><i class="fas fa-user"></i> Patient Info</h3>
                <div class="image-info" id="imageInfo">
                    <div class="info-item">
                        <span class="info-label">Patient ID:</span>
                        <span class="info-value">{{ $report->patient_id }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Patient Name:</span>
                        <span class="info-value">{{ $report->patient_name }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Age:</span>
                        <span class="info-value">{{ $report->patient_age == '0' ? 'N/A' : preg_replace('/^0+/', '', $report->patient_age) }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Gender:</span>
                        <span class="info-value">{{ $report->patient_gender }}</span>
                    </div>
                    
                    
                    <div class="info-item">
                        <span class="info-label">Procedure:</span>
                        <span class="info-value">
                            {{ $report->procedure }}
                            @if($report->procedure_history)
                            @php
                                $procedure_history = $report->procedure_history;
                                if ($procedure_history == '@') {
                                    $procedure_history = '';
                                } elseif ($procedure_history) {
                                    // Remove "0;" patterns from the string
                                    $cleaned = preg_replace('/0;\s*/', '', $procedure_history);
                                    // If after cleaning we only have whitespace/semicolons, show N/A
                                    $procedure_history = preg_match('/^[;\s]*$/', $cleaned) ? 'N/A' : $cleaned;
                                } else {
                                    $procedure_history = '-';
                                }
                            @endphp
                            ({{ $procedure_history }})
                            @endif
                        </span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Clinical History:</span>
                        @php
                            $clinical_history = $report->clinical_history;
                            if ($clinical_history) {
                                // Remove "0;" patterns from the string
                                $cleaned = preg_replace('/0;\s*/', '', $clinical_history);
                                // If after cleaning we only have whitespace/semicolons, show N/A
                                $clinical_history = preg_match('/^[;\s]*$/', $cleaned) ? 'N/A' : $cleaned;
                            } else {
                                $clinical_history = 'N/A';
                            }
                        @endphp
                        <span class="info-value">{{ $clinical_history }}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Study Date:</span>
                        <span class="info-value">{{ date('d M Y', strtotime($report->study_time)) }}</span>
                    </div>
                </div>
            </div>

             <!-- Write Report Button -->
            <div class="sidebar-write-report">
                @if(auth()->user()->type == \App\Constants\UserType::RADIOLOGIST)
                    <button class="btn btn-primary me-2" id="writeReportBtnSidebar" title="Write Report">
                        <i class="fas fa-edit me-1"></i>Write Report
                    </button>
                @endif
            </div>
        </aside>

        <!-- Viewer Area -->
        <div class="viewer-area">
            <!-- Toolbar -->
            <div class="toolbar">
                <div class="tool-group">
                    <button class="tool-btn" data-tool="WindowLevel" title="Window/Level (W)">
                        <i class="fas fa-adjust"></i>
                    </button>
                    <button class="tool-btn" data-tool="Pan" title="Pan (P)">
                        <i class="fas fa-hand-paper"></i>
                    </button>
                    <button class="tool-btn" data-tool="Zoom" title="Zoom (Z)">
                        <i class="fas fa-search-plus"></i>
                    </button>
                    <button class="tool-btn" data-tool="StackScroll" title="Stack Scroll (S)">
                        <i class="fas fa-layer-group"></i>
                    </button>
                </div>

                <div class="tool-group">
                    <button class="tool-btn" data-tool="Length" title="Length Measurement (L)">
                        <i class="fas fa-ruler"></i>
                    </button>
                    <button class="tool-btn" data-tool="Angle" title="Angle Measurement (A)">
                        <i class="fas fa-drafting-compass"></i>
                    </button>
                    <button class="tool-btn" data-tool="Rectangle" title="Rectangle ROI (R)">
                        <i class="fas fa-square"></i>
                    </button>
                    <button class="tool-btn" data-tool="Ellipse" title="Ellipse ROI (E)">
                        <i class="fas fa-circle"></i>
                    </button>
                    <button class="tool-btn" data-tool="Probe" title="Probe (Ctrl+P)">
                        <i class="fas fa-crosshairs"></i>
                    </button>
                </div>

                <div class="tool-group">
                    <button class="tool-btn" data-action="rotate-left" title="Rotate Left">
                        <i class="fas fa-undo"></i>
                    </button>
                    <button class="tool-btn" data-action="rotate-right" title="Rotate Right">
                        <i class="fas fa-redo"></i>
                    </button>
                    <button class="tool-btn" data-action="flip-horizontal" title="Flip Horizontal">
                        <i class="fas fa-arrows-alt-h"></i>
                    </button>
                    <button class="tool-btn" data-action="flip-vertical" title="Flip Vertical">
                        <i class="fas fa-arrows-alt-v"></i>
                    </button>
                </div>

                <div class="tool-group">
                    <button class="tool-btn" data-action="reset" title="Reset View (Ctrl+R)">
                        <i class="fas fa-home"></i>
                    </button>
                    <button class="tool-btn" data-action="fit" title="Fit to Window (F)">
                        <i class="fas fa-expand-arrows-alt"></i>
                    </button>
                    <button class="tool-btn" data-action="invert" title="Invert Colors (I)">
                        <i class="fas fa-adjust"></i>
                    </button>
                    <button class="tool-btn" data-action="clear-annotations" title="Clear Annotations">
                        <i class="fas fa-eraser"></i>
                    </button>
                </div>
            </div>

            <!-- Viewer Container -->
            <div class="viewer-container">
                <div class="viewport-wrapper">
                    <div id="viewport" class="viewport"></div>
                    <div class="viewport-overlay">
                        <div class="viewport-info top-left">
                            <div class="patient-info" id="patientInfo"></div>
                        </div>
                        <div class="viewport-info top-right">
                            <div class="image-info-overlay" id="imageInfoOverlay"></div>
                        </div>
                        <div class="viewport-info bottom-left">
                            <div class="window-level-info" id="windowLevelInfo"></div>
                        </div>
                        <div class="viewport-info bottom-right">
                            <div class="zoom-info" id="zoomInfo"></div>
                        </div>
                    </div>

                    <!-- Frame Navigation for Multi-frame DICOM -->
                    <div class="frame-navigation" id="frameNavigation" style="display: none;">
                        <button class="frame-btn" onclick="previousFrame()" title="Previous Frame (←)">
                            <i class="fas fa-chevron-left"></i>
                        </button>
                        <span class="frame-info" id="frameInfo">Frame 1 of 1</span>
                        <button class="frame-btn" onclick="nextFrame()" title="Next Frame (→)">
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                </div>

                <!-- Loading indicator -->
                <div class="loading-indicator" id="loadingIndicator">
                    <div class="spinner"></div>
                    <p>Loading Report File...</p>
                </div>

                <!-- Welcome screen -->
                <div class="welcome-screen" id="welcomeScreen">
                    <div class="welcome-content">
                        <i class="fas fa-x-ray welcome-icon"></i>
                        <div class="spinner"></div>
                        <p>Loading Report File...</p>
                    </div>
                </div>
            </div>
        </div>
    </main>
</div>

<!-- Modals -->
<div class="modal" id="metadataModal">
    <div class="modal-content">
        <div class="modal-header">
            <h3><i class="fas fa-tags"></i> DICOM Metadata</h3>
            <button class="modal-close" onclick="closeModal('metadataModal')">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="modal-body">
            <div class="metadata-content" id="metadataContent">
                <div class="loading">Loading metadata...</div>
            </div>
        </div>
    </div>
</div>

{{-- <div class="modal" id="aboutModal">
    <div class="modal-content">
        <div class="modal-header">
            <h3><i class="fas fa-info-circle"></i> About</h3>
            <button class="modal-close" onclick="closeModal('aboutModal')">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="modal-body">
            <div class="about-content">
                <h4>Modern DICOM Viewer</h4>
                <p>A professional DICOM viewer built with Cornerstone.js</p>
                <h5>Features:</h5>
                <ul>
                    <li>Window/Level adjustment</li>
                    <li>Pan, Zoom, and Stack scrolling</li>
                    <li>Multi-frame DICOM support</li>
                    <li>Measurement tools (Length, Angle)</li>
                    <li>ROI tools (Rectangle, Ellipse)</li>
                    <li>Image transformations (Rotate, Flip, Invert)</li>
                    <li>DICOM metadata viewer</li>
                    <li>Keyboard shortcuts</li>
                    <li>Dark/Light theme</li>
                </ul>
                <h5>Keyboard Shortcuts:</h5>
                <ul>
                    <li><kbd>W</kbd> - Window/Level tool</li>
                    <li><kbd>P</kbd> - Pan tool</li>
                    <li><kbd>Z</kbd> - Zoom tool</li>
                    <li><kbd>L</kbd> - Length measurement</li>
                    <li><kbd>F</kbd> - Fit to window</li>
                    <li><kbd>I</kbd> - Invert colors</li>
                    <li><kbd>Ctrl+R</kbd> - Reset view</li>
                    <li><kbd>←</kbd> - Previous frame</li>
                    <li><kbd>→</kbd> - Next frame</li>
                </ul>
            </div>
        </div>
    </div>
</div> --}}

<!-- Write Report Modal -->
<div class="modal fade" id="writeReportModal" tabindex="-1" aria-labelledby="writeReportModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="writeReportModalLabel">
                    <i class="bi bi-file-text text-primary me-2"></i>Write Report
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- Patient Information -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card shadow-sm">
                            <div class="card-header py-3">
                                <h6 class="m-0 font-weight-bold text-primary">
                                    <i class="bi bi-person me-2"></i>Patient Information
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3">
                                        <strong>Patient ID:</strong><br>
                                        <span class="text-muted">{{ $report->patient_id }}</span>
                                    </div>
                                    <div class="col-md-3">
                                        <strong>Patient Name:</strong><br>
                                        <span class="text-muted">{{ $report->patient_name }}</span>
                                    </div>
                                    <div class="col-md-3">
                                        <strong>Age:</strong><br>
                                        <span class="text-muted">{{ $report->patient_age == '0' ? '-' : preg_replace('/^0+/', '', $report->patient_age) }}</span>
                                    </div>
                                    <div class="col-md-3">
                                        <strong>Gender:</strong><br>
                                        <span class="text-muted">{{ $report->patient_gender }}</span>
                                    </div>
                                </div>
                                <hr>
                                <div class="row">
                                    <div class="col-md-3">
                                        <strong>Procedure:</strong><br>
                                        <span class="text-muted">
                                            {{ $report->procedure }}
                                            @if($report->procedure_history)
                                            @php
                                                $procedure_history = $report->procedure_history;
                                                if ($procedure_history == '@') {
                                                    $procedure_history = '';
                                                } elseif ($procedure_history) {
                                                    // Remove "0;" patterns from the string
                                                    $cleaned = preg_replace('/0;\s*/', '', $procedure_history);
                                                    // If after cleaning we only have whitespace/semicolons, show N/A
                                                    $procedure_history = preg_match('/^[;\s]*$/', $cleaned) ? 'N/A' : $cleaned;
                                                } else {
                                                    $procedure_history = '-';
                                                }
                                            @endphp
                                            ({{ $procedure_history }})
                                            @endif
                                        </span>
                                    </div>
                                    <div class="col-md-3">
                                        <strong>Clinical History:</strong><br>
                                        @php
                                            $clinical_history = $report->clinical_history;
                                            if ($clinical_history) {
                                                // Remove "0;" patterns from the string
                                                $cleaned = preg_replace('/0;\s*/', '', $clinical_history);
                                                // If after cleaning we only have whitespace/semicolons, show N/A
                                                $clinical_history = preg_match('/^[;\s]*$/', $cleaned) ? 'N/A' : $cleaned;
                                            } else {
                                                $clinical_history = 'N/A';
                                            }
                                        @endphp
                                        <span class="text-muted">{{ $clinical_history }}</span>
                                    </div>
                                    <div class="col-md-3">
                                        <strong>Study Date:</strong><br>
                                        <span class="text-muted">{{ date('d M Y', strtotime($report->study_time)) }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Report Writing Section -->
                <div class="row">
                    <div class="col-12">
                        <div class="card shadow-sm">
                            <div class="card-header py-3">
                                <div class="row align-items-center">
                                    <div class="col-md-6">
                                        <h6 class="m-0 font-weight-bold text-success">
                                            <i class="bi bi-file-text me-2"></i>Radiology Report
                                        </h6>
                                    </div>

                                    @if(auth()->user()->type == \App\Constants\UserType::RADIOLOGIST)
                                    <div class="col-md-6 text-end">
                                        <div class="d-flex gap-2 justify-content-end">
                                            <button class="btn btn-sm btn-outline-secondary" onclick="showSaveTemplateModalDialog()">
                                                <i class="bi bi-save me-1"></i>Save as Template
                                            </button>
                                        </div>
                                    </div>
                                    @endif

                                </div>
                            </div>
                            <div class="card-body">
                                <form id="reportFormModal">

                                    @if(auth()->user()->type == \App\Constants\UserType::RADIOLOGIST)
                                    <!-- Report Template Selection -->
                                    <div class="row mb-3">
                                        <div class="col-md-12">
                                            <label for="reportTemplateModal" class="form-label fw-bold">Report Template</label>
                                            <select class="form-select" id="reportTemplateModal" name="template">
                                                <option value="">Select Saved Template (Optional)</option>
                                                @foreach ($feedback_templates as $template)
                                                    <option value="{{ $template->description }}">{{ $template->name }}</option>
                                                @endforeach
                                            </select>
                                        </div>
                                    </div>
                                    @endif

                                    <!-- Report Content -->
                                    <div class="mb-3">
                                        <label for="reportContentModal" class="form-label fw-bold">Report Content</label>
                                        <textarea @if($report->status == \App\Constants\LabReportStatus::COMPLETED) readonly @endif class="form-control" id="reportContentModal" name="report_content" rows="15"
                                                  placeholder="Enter your radiology report here..." required>{{ $report->report_feedback }}</textarea>
                                        <div class="form-text">Use standard radiology reporting format including Technique, Findings, and Impression sections.</div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="bi bi-x-circle me-1"></i>Cancel
                </button>
                <button type="button" class="btn btn-success" id="submitReportModal" @if($report->status == \App\Constants\LabReportStatus::COMPLETED) disabled @endif>
                    <i class="bi bi-check-circle me-1"></i>@if($report->status == \App\Constants\LabReportStatus::COMPLETED) Already Submitted @else Submit Report @endif
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Save Template Modal -->
<div class="modal fade" id="SaveTemplateModal" tabindex="-1" aria-labelledby="SaveTemplateModal" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-save text-primary me-2"></i>Save Report Template
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label for="templateNameModal" class="form-label">Report Template Name <span class="text-danger">*</span></label>
                    <input type="text" class="form-control" id="templateNameModal" name="templateName" placeholder="Enter template name" required>
                    <div class="form-text">Choose a descriptive name for this report template</div>
                </div>
                <div class="mt-3">
                    <small class="text-muted">
                        <i class="bi bi-info-circle me-1"></i>
                        This will save the current report content as a reusable template.
                    </small>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="bi bi-x-circle me-1"></i>Cancel
                </button>
                <button type="button" class="btn btn-primary" id="confirmSaveTemplateModal">
                    <i class="bi bi-save me-1"></i>Save Template
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Scripts -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.6/dist/js/bootstrap.bundle.min.js"></script>
<script src="{{ url()->asset('assets/js/dicom_viewer.js?v=250803') }}"></script>

<script>

    // Hide preloader when DICOM viewer is ready
    function hidePreloader() {
        const preloader = document.getElementById('pagePreloader');
        if (preloader && preloader.style.display !== 'none') {
            preloader.classList.add('fade-out');
            setTimeout(() => {
                preloader.style.display = 'none';
            }, 300);
        }
    }

    // Make hidePreloader available globally for DICOM viewer
    window.hidePreloader = hidePreloader;

    // Fix viewport height calculation on page load
    function fixViewportHeight() {
        const viewerArea = document.querySelector('.viewer-area');
        const viewport = document.getElementById('viewport');

        if (viewerArea && viewport && window.cornerstone) {
            // Force a layout recalculation
            setTimeout(() => {
                try {
                    cornerstone.resize(viewport);
                } catch (e) {
                    // Cornerstone might not be ready yet
                }
            }, 100);

            // Additional resize after a longer delay to ensure layout is stable
            setTimeout(() => {
                try {
                    cornerstone.resize(viewport);
                } catch (e) {
                    // Cornerstone might not be ready yet
                }
            }, 500);
        }
    }

    // Call fixViewportHeight when page is fully loaded
    window.addEventListener('load', fixViewportHeight);

    // Also call it when DOM is ready
    document.addEventListener('DOMContentLoaded', fixViewportHeight);

    // Fallback timer only in case something goes wrong
    setTimeout(hidePreloader, 5000);

    // Add pinch-to-zoom functionality for mobile
    function addPinchToZoom() {
        const viewport = document.getElementById('viewport');
        if (!viewport) return;

        let initialDistance = 0;
        let initialZoom = 1;
        let isZooming = false;

        function getDistance(touches) {
            const dx = touches[0].clientX - touches[1].clientX;
            const dy = touches[0].clientY - touches[1].clientY;
            return Math.sqrt(dx * dx + dy * dy);
        }

        function handleTouchStart(e) {
            if (e.touches.length === 2) {
                e.preventDefault();
                isZooming = true;
                initialDistance = getDistance(e.touches);

                // Get current zoom level from cornerstone
                try {
                    const enabledElement = cornerstone.getEnabledElement(viewport);
                    if (enabledElement && enabledElement.viewport) {
                        initialZoom = enabledElement.viewport.scale || 1;
                    }
                } catch (error) {
                    initialZoom = 1;
                }
            }
        }

        function handleTouchMove(e) {
            if (e.touches.length === 2 && isZooming) {
                e.preventDefault();

                const currentDistance = getDistance(e.touches);
                const scale = currentDistance / initialDistance;
                const newZoom = initialZoom * scale;

                // Apply zoom constraints
                const minZoom = 0.1;
                const maxZoom = 10;
                const constrainedZoom = Math.max(minZoom, Math.min(maxZoom, newZoom));

                try {
                    const enabledElement = cornerstone.getEnabledElement(viewport);
                    if (enabledElement && enabledElement.viewport) {
                        enabledElement.viewport.scale = constrainedZoom;
                        cornerstone.setViewport(viewport, enabledElement.viewport);
                    }
                } catch (error) {
                    // Handle error silently
                }
            }
        }

        function handleTouchEnd(e) {
            if (e.touches.length < 2) {
                isZooming = false;
                initialDistance = 0;
                initialZoom = 1;
            }
        }

        // Add touch event listeners
        viewport.addEventListener('touchstart', handleTouchStart, { passive: false });
        viewport.addEventListener('touchmove', handleTouchMove, { passive: false });
        viewport.addEventListener('touchend', handleTouchEnd, { passive: false });
        viewport.addEventListener('touchcancel', handleTouchEnd, { passive: false });
    }

    // Initialize pinch-to-zoom when DOM is ready
    document.addEventListener('DOMContentLoaded', addPinchToZoom);

    document.addEventListener('DOMContentLoaded', function() {
        const reportTemplateModal = document.getElementById('reportTemplateModal');
        if (reportTemplateModal) {
            reportTemplateModal.addEventListener('change', function() {
                const reportContent = document.getElementById('reportContentModal');
                if (reportContent) {
                    reportContent.value = this.value;
                }
            });
        }
    });
</script>

<script>
    function showSaveTemplateModalDialog() {
        var modal = new bootstrap.Modal(document.getElementById('SaveTemplateModal'));
        modal.show();

        const confirmSaveTemplateModal = document.getElementById('confirmSaveTemplateModal');
        if (confirmSaveTemplateModal) {
            confirmSaveTemplateModal.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            var templateName = document.getElementById('templateNameModal').value;
            var templateContent = document.getElementById('reportContentModal').value;

            if (!templateName) {
                toastr.error('Please enter a template name');
                return;
            }

            if (!templateContent) {
                toastr.error('Please write some report content before saving as template');
                return;
            }

            modal.hide();
            $.ajax({
                url: '{{ url('/worklist/save-feedback-template') }}',
                type: 'POST',
                data: {
                    template_name: templateName,
                    description: templateContent,
                    _token: '{{ csrf_token() }}'
                },
                success: function(response) {
                    if (response.success) {
                        toastr.success(response.message);
                    } else {
                        toastr.error(response.message);
                    }
                },
                error: function(xhr) {
                    toastr.error(xhr.responseJSON?.message || 'Something went wrong');
                }
            });
        });
        }
    }
</script>

<script>

    // Initialize other functionality when DOM is ready
    document.addEventListener('DOMContentLoaded', function() {
        // Write Report button functionality for both buttons
        function handleWriteReportClick(e) {
            e.preventDefault();
            e.stopPropagation();

            // Small delay to ensure modal works properly
            setTimeout(() => {
                const modalElement = document.getElementById('writeReportModal');
                const modal = new bootstrap.Modal(modalElement, {
                    backdrop: true,
                    keyboard: true,
                    focus: true
                });
                modal.show();
            }, 100);
        }

        // Add event listeners to both buttons
        const writeReportBtn = document.getElementById('writeReportBtn');
        const writeReportBtnSidebar = document.getElementById('writeReportBtnSidebar');

        if (writeReportBtn) {
            writeReportBtn.addEventListener('click', handleWriteReportClick);
        }

        if (writeReportBtnSidebar) {
            writeReportBtnSidebar.addEventListener('click', handleWriteReportClick);
        }

        // Report form functionality
        const submitReportModal = document.getElementById('submitReportModal');
        if (submitReportModal) {
            submitReportModal.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            const reportContent = document.getElementById('reportContentModal').value;

            if (!reportContent.trim()) {
                toastr.error('Report content cannot be empty');
                return;
            }

            // Show loading state
            const originalText = this.innerHTML;
            this.innerHTML = '<i class="bi bi-hourglass-split me-1"></i>Submitting...';
            this.disabled = true;

            // Send AJAX request
            $.ajax({
                url: '{{ url('worklist/'.$report->id.'/save-feedback') }}',
                type: 'POST',
                data: {
                    _token: '{{ csrf_token() }}',
                    report_content: reportContent
                },
                success: function(response) {
                    if (response.success) {
                        toastr.success(response.message);
                        setTimeout(function() {
                            window.location.href = '/worklist';
                        }, 1500);
                    } else {
                        toastr.error('An error occurred while saving report feedback');

                        // Update the displayed value
                        const submitBtn = document.getElementById('submitReportModal');
                        submitBtn.innerHTML = '<i class="bi bi-x-circle me-1"></i> Submit Report';
                        submitBtn.disabled = false;
                        document.getElementById('reportContentModal').readOnly = false;
                    }
                },
                error: function() {
                    toastr.error('An error occurred while saving report feedback');
                    // Update the displayed value
                    const submitBtn = document.getElementById('submitReportModal');
                    submitBtn.innerHTML = '<i class="bi bi-x-circle me-1"></i> Submit Report';
                    submitBtn.disabled = false;
                    document.getElementById('reportContentModal').readOnly = false;
                },
                complete: function() {
                    bootstrap.Modal.getInstance(document.getElementById('writeReportModal')).hide();
                }
            });
        });
        }

    });

    // Download function for images
    async function downloadCurrentImage(imageUrl, fileName) {
        try {
            toastr.info('Preparing download...');

            // Fetch the image as a blob
            const response = await fetch(imageUrl, {
                method: 'GET',
                headers: {
                    'Accept': 'image/*'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const blob = await response.blob();

            // Get the file extension from the original URL or use jpg as default
            const urlParts = imageUrl.split('.');
            const extension = urlParts.length > 1 ? urlParts[urlParts.length - 1].toLowerCase() : 'jpg';
            const fullFileName = `${fileName}.${extension}`;

            // Create a blob URL and download link
            const blobUrl = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = blobUrl;
            link.download = fullFileName;

            // Append to body, click, and remove
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            // Clean up the blob URL
            window.URL.revokeObjectURL(blobUrl);

            toastr.success('Download completed');
        } catch (error) {
            console.error('Download error:', error);
            toastr.error('Failed to download image: ' + error.message);
        }
    }
</script>
</body>
</html>
