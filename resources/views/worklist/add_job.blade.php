@extends('master')

@section('title')
    Add Job
@endsection

@section('header')
<style>
    #addJobForm label {
        font-weight: bold;
    }
    #addJobForm .form-group {
        margin-bottom: 15px;
    }
</style>
@endsection

@section('page_id')
    page-add-job
@endsection

@section('page_content')

    <!-- Main Content -->
    <div class="container-fluid py-4">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col">
                <div class="d-flex align-items-center">
                    <a href="{{ url('/worklist') }}" class="btn btn-outline-secondary me-3">
                        <i class="bi bi-arrow-left me-1"></i>Back to Worklist
                    </a>
                    <div>
                        <h1 class="h3 mb-0">Add New Job</h1>
                        <p class="text-muted mb-0">Create a new worklist item for medical imaging</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Add Job Form -->
        <div class="row">
            <div class="col-12">
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="bi bi-plus-circle me-2"></i>Job Information
                        </h6>
                    </div>
                    <div class="card-body">
                        <form id="addJobForm" enctype="multipart/form-data" method="POST">

                            @csrf



                            <!-- Patient Information -->
                            <div class="row mb-4">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label for="patientId" class="form-label">Patient ID <span class="text-danger">*</span></label>
                                        <input value="{{ old('patient_id') }}" type="text" class="form-control" name="patient_id" required placeholder="Enter Patient ID">
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label for="patientName" class="form-label">Patient Name <span class="text-danger">*</span></label>
                                        <input value="{{ old('patient_name') }}" type="text" class="form-control" name="patient_name" required placeholder="Enter Patient Name">
                                    </div>
                                </div>

                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label class="form-label">Patient Age <span class="text-danger">*</span></label>
                                        <input value="{{ old('patient_age') }}" type="text" class="form-control" name="patient_age" required placeholder="Enter Patient Age">
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label for="arrivalDateTime" class="form-label">Arrival Date/Time <span class="text-danger">*</span></label>
                                        <input value="{{ old('arrival_time', now()->format('Y-m-d\TH:i')) }}" type="datetime-local" class="form-control" id="arrivalDateTime" name="arrival_time" required>
                                    </div>
                                </div>
                            </div>

                            <div class="row mb-4">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label class="form-label">Patient Gender <span class="text-danger">*</span></label>
                                        <select class="form-select" name="patient_gender" required>
                                            <option value="">Select Gender</option>
                                            <option value="M" @if(old('patient_gender') == 'M') selected @endif>Male</option>
                                            <option value="F" @if(old('patient_gender') == 'F') selected @endif>Female</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="row mb-4">
                                <div class="col-md-10">
                                    <div class="form-group">
                                        <label for="procedure" class="form-label">Referred by (Doctor Name)</label>
                                        <select class="form-select select2" id="doctor" name="doctor" data-placeholder="Select Doctor">
                                            <option value="0">Select Doctor</option>
                                            @foreach ($doctors as $doctor)
                                                <option value="{{ $doctor->id }}" @if(old('doctor') == $doctor->id) selected @endif>{{ $doctor->name }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="form-group">
                                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addDoctorModal" style="margin-top: 30px;">
                                            <i class="bi bi-plus"></i> Add Doctor
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="row mb-4">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label for="procedure" class="form-label">Procedure <span class="text-danger">*</span></label>
                                        <select class="form-select select2" id="procedure" name="procedure" data-placeholder="Select Procedure" required>
                                            <option value="">Select Procedure</option>
                                            @foreach ($procedures as $procedure)
                                                <option value="{{ $procedure->name }}" @if(old('procedure') == $procedure->name) selected @endif>{{ $procedure->name }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="row mb-4">
                                <div class="col-md-10">
                                    <div class="form-group">
                                        <label for="clinicalHistory" class="form-label">Clinical History <span class="text-danger">*</span></label>
                                        <select class="form-select select2" id="clinical_history" name="clinical_history" data-placeholder="Select Clinical History">
                                            <option value="">Select Clinical History</option>
                                            @foreach ($clinical_histories as $history)
                                                <option value="{{ $history->history }}" @if(old('clinical_history') == $history->history) selected @endif>{{ $history->history }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <div class="form-group">
                                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addClinicalHistoryModal" style="margin-top: 30px;">
                                            <i class="bi bi-plus"></i> Add Clinical History
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <div class="row mb-4">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label class="form-label">Hospital <span class="text-danger">*</span></label>
                                        <select class="form-select" name="tenant" required>
                                            @if(auth()->user()->type == \App\Constants\UserType::TENANT)
                                                <option value="{{ auth()->user()->id }}" selected>{{ auth()->user()->name }}</option>
                                            @else
                                                <option value="">Select Hospital</option>
                                                @foreach ($tenants as $tenant)
                                                    <option value="{{ $tenant->id }}" @if(old('tenant') == $tenant->id) selected @endif>{{ $tenant->name }}</option>
                                                @endforeach
                                            @endif
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label for="modality" class="form-label">Modality <span class="text-danger">*</span></label>
                                        <select class="form-select" id="modality" name="modality" required>
                                            <option value="">Select Modality</option>
                                            <option value="DX" @if(old('modality') == 'DX') selected @endif>DX - Digital Radiography</option>
                                            <option value="DR" @if(old('modality') == 'DR') selected @endif>DR - Digital Radiography</option>
                                            <option value="CR" @if(old('modality') == 'CR') selected @endif>CR - Computed Radiography</option>
                                            <option value="CT" @if(old('modality') == 'CT') selected @endif>CT - Computed Tomography</option>
                                            <option value="MR" @if(old('modality') == 'MR') selected @endif>MR - Magnetic Resonance</option>
                                            <option value="US" @if(old('modality') == 'US') selected @endif>US - Ultrasound</option>
                                            <option value="ECG" @if(old('modality') == 'ECG') selected @endif>ECG - Electrocardiogram</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <!-- Upload Files Section -->
                            <div class="row mb-4">
                                <div class="col-12">
                                    <div class="card border-secondary">
                                        <div class="card-header bg-light">
                                            <h6 class="mb-0 text-secondary">
                                                <i class="bi bi-cloud-upload me-2"></i>Upload Files
                                            </h6>
                                        </div>
                                        <div class="card-body">
                                            <div class="row">
                                                {{-- <div class="col-md-5">
                                                    <div class="mb-3">
                                                        <label for="dicomFiles" class="form-label">DICOM File</label>
                                                        <input type="file" class="form-control" id="dicomFile" name="dicomFile" accept=".dcm,.dicom">
                                                        <div class="form-text">Select DICOM file (.dcm, .dicom)</div>
                                                    </div>
                                                </div>
                                                <div class="col-md-2" style="text-align: center;margin-top: 40px;">
                                                    <label class="form-label">or</label>
                                                </div> --}}
                                                <div class="col-md-12">
                                                    <div class="mb-0">
                                                        <label for="additionalFiles" class="form-label">JPG/PNG Files</label>
                                                        <input type="file" class="form-control" id="otherFiles" name="otherFiles[]" multiple>
                                                        <div class="form-text">Select files</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Form Actions -->
                            <div class="row">
                                <div id="formActions" class="col-12 mb-4">
                                    <div class="d-flex gap-2">
                                        <a href="{{ url('/worklist') }}" class="btn btn-secondary">
                                            <i class="bi bi-x-circle me-1"></i>Cancel
                                        </a>
                                        <button type="reset" class="btn btn-outline-secondary">
                                            <i class="bi bi-arrow-clockwise me-1"></i>Reset
                                        </button>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="bi bi-plus-circle me-1"></i>Add Job
                                        </button>
                                    </div>
                                </div>
                                <!-- Upload Progress -->
                                <div id="uploadProgressContainer" style="display: none;">
                                    <label>Uploading...</label>
                                    <div class="progress mb-3">
                                        <div id="uploadProgressBar" class="progress-bar progress-bar-striped progress-bar-animated"
                                             role="progressbar" style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">
                                            0%
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>


    <div class="modal fade" id="addDoctorModal" tabindex="-1" aria-labelledby="addDoctorModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addDoctorModalLabel">Add Doctor</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form>
                        <div class="form-group">
                            <label for="name">Name</label>
                            <input type="text" class="form-control" id="doctorName" name="doctorName" required>
                        </div>
                        <div class="form-group mt-3">
                            <button id="addDoctorBtn" type="submit" class="btn btn-primary">Add</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" id="addClinicalHistoryModal" tabindex="-1" aria-labelledby="addClinicalHistoryModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Add Clinical History</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form>
                        <div class="form-group">
                            <label for="name">History</label>
                            <input type="text" class="form-control" id="clinic_history_form_text" name="clinic_history_form_text" required>
                        </div>
                        <div class="form-group mt-3">
                            <button id="addClinicalHistoryBtn" type="submit" class="btn btn-primary">Add</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

@endsection

@section('footer')
    <script>
        $(document).ready(function () {
            $('#procedure').select2({
                theme: 'bootstrap-5',
                placeholder: "Select Procedure",
                allowClear: true
            });
            $('#doctor').select2({
                theme: 'bootstrap-5',
                placeholder: "Select Doctor",
                allowClear: true
            });
        });
    </script>
    <script>
        $(document).ready(function () {
            $('#addDoctorBtn').click(function (e) {
                e.preventDefault();

                const name = $('#doctorName').val();

                $.ajax({
                    url: '{{ url("worklist/add-doctor") }}',
                    type: 'POST',
                    data: {
                        name: name,
                        _token: '{{ csrf_token() }}'
                    },
                    success: function (response) {
                        if (response.success) {
                            toastr.success(response.message);

                            $('#doctor').append(
                                $('<option>', {
                                    value: response.doctor_id,
                                    text: response.doctor_name,
                                    selected: true
                                })
                            ).trigger('change'); // refresh select2

                            bootstrap.Modal.getOrCreateInstance(document.getElementById('addDoctorModal')).hide();
                            $('#doctorName').val('');
                        }else {
                            toastr.error(response.message);
                        }
                    },
                    error: function (xhr, status, error) {
                        toastr.error(xhr.responseJSON?.message || 'An error occurred');
                    }
                });
            });
        });
    </script>

    <script>
        $(document).ready(function () {
            $('#addClinicalHistoryBtn').click(function (e) {
                e.preventDefault();

                const history = $('#clinic_history_form_text').val();

                $.ajax({
                    url: '{{ url("worklist/add-clinical-history") }}',
                    type: 'POST',
                    data: {
                        history: history,
                        _token: '{{ csrf_token() }}'
                    },
                    success: function (response) {
                        if (response.success) {
                            toastr.success(response.message);

                            $('#clinical_history').append(
                                $('<option>', {
                                    value: response.history,
                                    text: response.history,
                                    selected: true
                                })
                            ).trigger('change'); // refresh select2

                            bootstrap.Modal.getOrCreateInstance(document.getElementById('addClinicalHistoryModal')).hide();
                            $('#clinic_history_form_text').val('');
                        }else {
                            toastr.error(response.message);
                        }
                    },
                    error: function (xhr, status, error) {
                        toastr.error(xhr.responseJSON?.message || 'An error occurred');
                    }
                });
            });
        });
    </script>

    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const form = document.getElementById('addJobForm');
            const progressBar = document.getElementById('uploadProgressBar');
            const progressContainer = document.getElementById('uploadProgressContainer');
            const actionsContainer = document.getElementById('formActions');

            form.addEventListener('submit', function (e) {
                e.preventDefault();

                const formData = new FormData(form);
                const xhr = new XMLHttpRequest();

                xhr.open('POST', '{{ url("worklist/add-job") }}', true);
                xhr.setRequestHeader('X-CSRF-TOKEN', '{{ csrf_token() }}');

                // Show progress container
                progressContainer.style.display = 'block';
                actionsContainer.style.display = 'none';

                // Upload progress
                xhr.upload.addEventListener('progress', function (e) {
                    if (e.lengthComputable) {
                        const percentComplete = Math.round((e.loaded / e.total) * 100);
                        progressBar.style.width = percentComplete + '%';
                        progressBar.setAttribute('aria-valuenow', percentComplete);
                        progressBar.innerText = percentComplete + '%';
                    }
                });

                // Success response
                xhr.onload = function () {
                    if (xhr.status === 200) {
                        const res = JSON.parse(xhr.responseText);
                        if (res.success) {
                            toastr.success('Job has been added successfully! Redirecting to worklist page...');
                            window.location.href = '/worklist';
                        } else {
                            toastr.error(res.message || 'Failed to add job');
                        }
                    }
                };

                // Error response
                xhr.onerror = function () {
                    toastr.error('Something went wrong');
                };

                xhr.send(formData);
            });
        });
    </script>
@endsection
