@extends('master')

@section('title')
    Update Hospital
@endsection

@section('header')

@endsection

@section('page_id')
    page-update-tenant
@endsection

@section('page_content')

    <!-- Main Content -->
    <div class="container-fluid py-4">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col">
                <h1 class="h3 mb-0" id="pageTitle">Update Hospital</h1>
                <p class="text-muted" id="pageDescription">Update hospital organization account</p>
            </div>
            <div class="col-auto">
                <a href="{{ url('users') }}" class="btn btn-secondary">
                    <i class="bi bi-arrow-left me-1"></i>Back to Users
                </a>
            </div>
        </div>

        <!-- Tenant Form -->
        <div class="row">
            <div class="col-12">
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="bi bi-building me-2"></i>Hospital Information
                        </h6>
                    </div>
                    <div class="card-body">
                        <form id="tenantUserForm" action="{{ url('users/update-tenant/' . $user->id) }}" method="POST">

                            @csrf

                            <div class="row">
                                <div class="col-md-12 mb-3">
                                    <label for="tenantFullName" class="form-label fw-bold">Hospital/Organization Name <span class="text-danger">*</span></label>
                                    <input value="{{ $user->name }}" type="text" class="form-control" id="tenantFullName" name="name" required>
                                </div>
                                <div class="col-md-12 mb-3">
                                    <label for="tenantShortName" class="form-label fw-bold">Hospital/Organization ID <span class="text-danger">*</span></label>
                                    <input disabled value="{{ $user->username }}" type="text" placeholder="Write user ID without any spaces" class="form-control" id="tenantShortName" name="username" required>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-12 mb-3">
                                    <label for="tenantAddress" class="form-label fw-bold">Address</label>
                                    <textarea class="form-control" id="tenantAddress" name="address" rows="3">{{ $user->address }}</textarea>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label class="form-label fw-bold">Upazila</label>
                                    <select class="form-select" id="upazila" name="upazila" required>
                                        <option value="">Select Upazila</option>
                                        @foreach ($upazilas as $data)
                                            <option value="{{ $data }}" @if($user->upazila == $data) selected @endif>{{ $data }}</option>
                                        @endforeach
                                    </select>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label class="form-label fw-bold">District</label>
                                    <select class="form-select" id="district" name="district" required>
                                        <option value="">Select District</option>
                                        @foreach ($districts as $data)
                                            <option value="{{ $data }}" @if($user->district == $data) selected @endif>{{ $data }}</option>
                                        @endforeach
                                    </select>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label class="form-label fw-bold">Division</label>
                                    <select class="form-select" id="division" name="division" required>
                                        <option value="">Select Division</option>
                                        @foreach ($divisions as $data)
                                            <option value="{{ $data }}" @if($user->division == $data) selected @endif>{{ $data }}</option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-12 mb-3">
                                    <label for="tenantMobileNo" class="form-label fw-bold">Hospital/Organization Phone No</label>
                                    <input value="{{ $user->mobile }}" type="tel" class="form-control" id="tenantMobileNo" name="mobile">
                                </div>
                                <div class="col-md-12 mb-3">
                                    <label for="tenantFax" class="form-label fw-bold">Fax</label>
                                    <input value="{{ $user->fax }}" type="text" class="form-control" id="tenantFax" name="fax">
                                </div>
                                <div class="col-md-12 mb-3">
                                    <label for="tenantEmail" class="form-label fw-bold">Email</label>
                                    <input value="{{ $user->email }}" type="email" class="form-control" id="tenantEmail" name="email">
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-12 mb-3">
                                    <label for="tenantContactPerson" class="form-label fw-bold">Contact Person Name</label>
                                    <input value="{{ $user->contact_name }}" type="text" class="form-control" id="tenantContactPerson" name="contact_name">
                                </div>
                                <div class="col-md-12 mb-3">
                                    <label for="tenantPhoneNo" class="form-label fw-bold">Contact Person Phone No</label>
                                    <input value="{{ $user->contact_mobile }}" type="tel" class="form-control" id="tenantPhoneNo" name="contact_mobile">
                                </div>
                            </div>

                            <!-- Password Fields -->
                            <div class="row">
                                <div class="col-md-12 mb-3">
                                    <label for="tenantPassword" class="form-label fw-bold">Password (Enter if you want to change)</label>
                                    <input value="{{ old('password') }}" type="password" class="form-control" id="tenantPassword" name="password">
                                </div>
                                <div class="col-md-12 mb-3">
                                    <label for="tenantConfirmPassword" class="form-label fw-bold">Confirm Password</label>
                                    <input type="password" class="form-control" id="tenantConfirmPassword" name="confirm_password">
                                </div>
                            </div>

                            <!-- Allowed Modalities -->
                            <div class="row">
                                <div class="col-md-12 mb-3">
                                    <label class="form-label fw-bold">Allowed Modalities & Rates</label>
                                    <div class="mt-2">

                                        @foreach ($modalities as $key => $modality)
                                            <div class="row mb-2">
                                                <div class="col-md-7">
                                                    <div class="row">
                                                        <div class="col-md-8">
                                                            <div class="form-check">
                                                                <label class="form-check-label" for="modality">{{ $modality }}</label>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-4">
                                                            <div class="input-group input-group-sm">
                                                                <span class="input-group-text">BDT</span>
                                                                <input value="{{ $user->modalities_n_rates->{$key} ?? 0 }}" type="number" class="form-control" name="rates[{{ $key }}]" placeholder="0" min="1" step="1" required>
                                                            </div>
                                                        </div>
                                                        <hr style="margin: 10px 0 0 0; padding: 0">
                                                    </div>
                                                </div>
                                            </div>
                                        @endforeach

                                    </div>
                                </div>
                            </div>

                            <!-- Radiologist Management Permission and Status -->
                            <div class="row">
                                <div class="col-md-12 mb-3">
                                    <label class="form-label fw-bold">Status</label>
                                    <div class="mt-2">
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="radio" name="status" id="tenantStatusActive" value="ACTIVE" @if( $user->status == 'ACTIVE') checked @endif>
                                            <label class="form-check-label" for="tenantStatusActive">
                                                Active
                                            </label>
                                        </div>
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="radio" name="status" id="tenantStatusInactive" value="INACTIVE" @if( $user->status == 'INACTIVE') checked @endif>
                                            <label class="form-check-label" for="tenantStatusInactive">
                                                Inactive
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="text-start mb-4">
                                <button type="submit" class="btn btn-primary" id="submitBtn">
                                    <i class="bi bi-building-add me-1"></i><span id="submitText">Update Hospital</span>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

@endsection

@section('footer')
    <script>
        $(document).ready(function () {
            $('#upazila').select2({
                theme: 'bootstrap-5',
                placeholder: "Select Upazila",
                allowClear: true
            });
            $('#district').select2({
                theme: 'bootstrap-5',
                placeholder: "Select District",
                allowClear: true
            });
            $('#division').select2({
                theme: 'bootstrap-5',
                placeholder: "Select Division",
                allowClear: true
            });
        });
    </script>
@endsection
