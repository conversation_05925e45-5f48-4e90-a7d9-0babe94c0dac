@extends('master')

@section('title')
    Users
@endsection

@section('header')

@endsection

@section('page_id')
    page-users
@endsection

@section('page_content')

    <!-- Main Content -->
    <div class="container-fluid py-4">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col">
                <h1 class="h3 mb-0">Manage Users</h1>
                <p class="text-muted">View, edit, and manage all user accounts</p>
            </div>
            <div class="col-auto">
                <div class="cus-btn-group" role="group">
                    @if(auth()->user()->type == \App\Constants\UserType::ADMIN)
                    <a href="{{ url('users/add-user') }}" class="btn btn-dark">
                        <i class="bi bi-person-plus me-1"></i>Add User
                    </a>
                    @endif
                    <a href="{{ url('users/add-tenant') }}" class="btn btn-primary">
                        <i class="bi bi-building-add me-1"></i>Add Hospital
                    </a>
                    <a href="{{ url('users/add-radiologist') }}" class="btn btn-success">
                        <i class="bi bi-person-badge me-1"></i>Add Radiologist
                    </a>
                </div>
            </div>
        </div>

        <!-- User Type Filter -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card shadow-sm">
                    <div class="card-body py-3">

                        <form action="{{ url('users') }}" method="get">
                        <div class="row align-items-center">
                            <div class="col-auto">
                                <h6 class="mb-0 text-muted">
                                    <i class="bi bi-funnel me-2"></i>Filter by User Type:
                                </h6>
                            </div>
                            <div class="col-md-6">
                                <div class="btn-group w-100" role="group" id="userTypeFilter">
                                    <input type="radio" class="btn-check" name="filterType" id="filterAll" value="all" @if(request('filterType') == 'all' || empty(request('filterType'))) checked @endif onclick="this.form.submit()">
                                    <label class="btn btn-outline-primary" for="filterAll">
                                        <i class="bi bi-people me-1"></i>All Users
                                    </label>

                                    @if(!in_array(auth()->user()->type, [\App\Constants\UserType::SALES, \App\Constants\UserType::MANAGER]))
                                    <input type="radio" class="btn-check" name="filterType" id="filterAdmin" value="USER" @if(request('filterType') == 'USER') checked @endif onclick="this.form.submit()">
                                    <label class="btn btn-outline-primary" for="filterAdmin">
                                        <i class="bi bi-shield-check me-1"></i>Users
                                    </label>
                                    @endif

                                    <input type="radio" class="btn-check" name="filterType" id="filterTenant" value="HOSPITAL" @if(request('filterType') == 'HOSPITAL') checked @endif onclick="this.form.submit()">
                                    <label class="btn btn-outline-primary" for="filterTenant">
                                        <i class="bi bi-building me-1"></i>Hospitals
                                    </label>

                                    <input type="radio" class="btn-check" name="filterType" id="filterRadiologist" value="RADIOLOGIST" @if(request('filterType') == 'RADIOLOGIST') checked @endif onclick="this.form.submit()">
                                    <label class="btn btn-outline-primary" for="filterRadiologist">
                                        <i class="bi bi-person-badge me-1"></i>Radiologists
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="input-group">
                                    <span class="input-group-text"><i class="bi bi-search"></i></span>
                                    <input name="search" value="{{ request()->get('search') }}" type="text" class="form-control" id="searchUsers" placeholder="Search users...">
                                    <button class="btn btn-outline-primary" type="submit">
                                        <i class="bi bi-search"></i> Search
                                    </button>
                                </div>
                            </div>
                        </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Users Table -->
        <div class="row">
            <div class="col-12">
                <div class="card shadow">
                    <div class="card-header py-3 d-flex justify-content-between align-items-center">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="bi bi-table me-2"></i>Users List
                        </h6>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0" id="usersTable">
                                <thead class="table-light">
                                <tr>
                                    <th class="border-0">User Type</th>
                                    <th class="border-0">Name</th>
                                    <th class="border-0">Email/Contact</th>
                                    <th class="border-0">Status</th>
                                    <th class="border-0">Created Date</th>
                                    <th class="border-0">Actions</th>
                                </tr>
                                </thead>
                                <tbody>
                                <!-- Users -->

                                @foreach($users as $user)
                                    @if(in_array(auth()->user()->type, [\App\Constants\UserType::SALES, \App\Constants\UserType::MANAGER]) && in_array($user->type, ['ADMIN', 'SALES', 'MANAGER']))
                                        @continue
                                    @endif
                                    <tr class="user-row">
                                        <td>

                                            @if($user->type == 'ADMIN' || $user->type == 'MANAGER' || $user->type == 'SALES')
                                                <span class="badge bg-primary">
                                                    <i class="bi bi-person me-1"></i>
                                                    User ({{ $user->type }})
                                                </span>
                                            @elseif($user->type == 'TENANT')
                                                <span class="badge bg-warning">
                                                    <i class="bi bi-building me-1"></i>
                                                    Hospital
                                                </span>
                                            @elseif($user->type == 'RADIOLOGIST')
                                                <span class="badge bg-success">
                                                    <i class="bi bi-person-badge me-1"></i>
                                                    Radiologist @if($user->is_internal) (Internal) @endif
                                                </span>
                                            @else
                                                <span class="badge bg-info">
                                                    <i class="bi bi-shield-check me-1"></i>
                                                    {{ $user->type }}
                                                </span>
                                            @endif

                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-sm bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-2">
                                                    <i class="bi bi-person"></i>
                                                </div>
                                                <div>
                                                    <div class="fw-bold">{{ $user->name }}</div>
                                                    <small class="text-muted">{{ $user->username }}</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div>{{ $user->email }}</div>
                                            <small class="text-muted">{{ $user->mobile }}</small>
                                        </td>
                                        <td>
                                            @if($user->status == 'ACTIVE')
                                                <span class="badge bg-success">Active</span>
                                            @else
                                                <span class="badge bg-danger">Inactive</span>
                                            @endif
                                        </td>
                                        <td>
                                            <small>{{ date('d M Y', strtotime($user->created_at)) }}</small>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                @if($user->type == \App\Constants\UserType::ADMIN || $user->type == \App\Constants\UserType::MANAGER || $user->type == \App\Constants\UserType::SALES)
                                                    <a href="{{ url('users/update-user/'.$user->id) }}" class="btn btn-outline-primary" title="Edit">
                                                        <i class="bi bi-pencil"></i>
                                                    </a>
                                                @elseif($user->type == \App\Constants\UserType::TENANT)
                                                    <a href="{{ url('users/update-tenant/'.$user->id) }}" class="btn btn-outline-primary" title="Edit">
                                                        <i class="bi bi-pencil"></i>
                                                    </a>
                                                @elseif($user->type == \App\Constants\UserType::RADIOLOGIST)
                                                    <a href="{{ url('users/update-radiologist/'.$user->id) }}" class="btn btn-outline-primary" title="Edit">
                                                        <i class="bi bi-pencil"></i>
                                                    </a>
                                                @endif
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach

                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

@endsection

@section('footer')
    <style>
        .avatar-sm {
            width: 32px;
            height: 32px;
            font-size: 14px;
        }

        .user-row {
            transition: background-color 0.2s ease;
        }

        .user-row:hover {
            background-color: rgba(0, 123, 255, 0.05);
        }

        .table th {
            font-weight: 600;
            color: #495057;
            border-bottom: 2px solid #dee2e6;
        }

        .badge {
            font-size: 0.75rem;
        }
    </style>
@endsection
