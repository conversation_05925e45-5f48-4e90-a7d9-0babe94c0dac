@extends('master')

@section('title')
    Add Radiologist
@endsection

@section('header')

@endsection

@section('page_id')
    page-add-radiologist
@endsection

@section('page_content')

    <!-- Main Content -->
    <div class="container-fluid py-4">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col">
                <h1 class="h3 mb-0" id="pageTitle">Add Radiologist</h1>
                <p class="text-muted" id="pageDescription">Create new radiologist account</p>
            </div>
            <div class="col-auto">
                <a href="{{ url('/users') }}" class="btn btn-secondary">
                    <i class="bi bi-arrow-left me-1"></i>Back to Users
                </a>
            </div>
        </div>

        <!-- Radiologist Form -->
        <div class="row">
            <div class="col-12">
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="bi bi-person-badge me-2"></i>Radiologist Information
                        </h6>
                    </div>
                    <div class="card-body">
                        <form id="radiologistUserForm" method="POST" action="{{ url('users/add-radiologist') }}" enctype="multipart/form-data">

                            @csrf

                            <div class="row">
                                <div class="col-md-12 mb-3">
                                    <label for="consultantName" class="form-label fw-bold">Consultant Name <span class="text-danger">*</span></label>
                                    <input value="{{ old('name') }}" type="text" class="form-control" id="consultantName" name="name" required>
                                </div>
                                <div class="col-md-12 mb-3">
                                    <label for="tenantShortName" class="form-label fw-bold">Radiologist ID <span class="text-danger">*</span></label>
                                    <input value="{{ old('username') }}" type="text" placeholder="Write user ID without any spaces" class="form-control" name="username" required>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-12 mb-3">
                                    <label for="radiologistEmail" class="form-label fw-bold">Email</label>
                                    <input value="{{ old('email') }}" type="email" class="form-control" id="radiologistEmail" name="email">
                                </div>
                                <div class="col-md-12 mb-3">
                                    <label for="radiologistPhone" class="form-label fw-bold">Phone No</label>
                                    <input value="{{ old('mobile') }}" type="tel" class="form-control" id="radiologistPhone" name="mobile">
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-12 mb-3">
                                    <label for="identityInfo" class="form-label fw-bold">Identity Information</label>
                                    <textarea class="form-control" id="identityInfo" name="identity_info" rows="4" placeholder="Enter identity information">{{ old('identity_info') }}</textarea>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-12 mb-3">
                                    <label for="bmdcRegNo" class="form-label fw-bold">BMDC Reg No</label>
                                    <input value="{{ old('bmdc_number') }}" type="text" class="form-control" id="bmdcRegNo" name="bmdc_number">
                                </div>
                            </div>
                            <!-- Signature Upload -->
                            <div class="row">
                                <div class="col-md-12 mb-3">
                                    <label class="form-label fw-bold">Signature</label>
                                    <div class="mt-2">
                                        <input type="file" class="form-control" id="signatureUpload" name="signature" accept="image/*" onchange="previewSignature(this)">
                                        <small class="form-text text-muted">Upload signature image (JPG, PNG, GIF)</small>

                                    </div>
                                </div>

                                <div class="col-md-12 mb-3">
                                    <!-- Signature Preview -->
                                    <div id="signaturePreview" class="mt-3" style="display: none;">
                                        <div class="d-flex align-items-center">
                                            <img id="signatureImage" src="" alt="Signature Preview" style="max-width: 200px; max-height: 100px; border: 1px solid #ddd; padding: 5px;">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-12 mb-3">
                                    <label for="radiologistPassword" class="form-label fw-bold">Password <span class="text-danger">*</span></label>
                                    <input value="{{ old('password') }}" type="password" class="form-control" id="radiologistPassword" name="password" required>
                                </div>
                                <div class="col-md-12 mb-3">
                                    <label for="radiologistConfirmPassword" class="form-label fw-bold">Confirm Password <span class="text-danger">*</span></label>
                                    <input value="{{ old('confirm_password') }}" type="password" class="form-control" id="radiologistConfirmPassword" name="confirm_password" required>
                                </div>
                            </div>

                            <!-- Internal and Status -->
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label class="form-label fw-bold">Internal</label>
                                    <div class="mt-2">
                                        <div class="form-check form-check-inline">
                                            <input id="radiologistInternalNo" class="form-check-input" type="radio" name="internal" value="NO" checked>
                                            <label class="form-check-label" for="radiologistInternalNo">No</label>
                                        </div>
                                        <div class="form-check form-check-inline">
                                            <input id="radiologistInternalYes" class="form-check-input" type="radio" name="internal" value="YES" @if(old('status') == 'YES') checked @endif>
                                            <label class="form-check-label" for="radiologistInternalYes">Yes</label>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-4 mb-3">
                                    <label class="form-label fw-bold">Status</label>
                                    <div class="mt-2">
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="radio" name="status" id="radiologistStatusActive" value="ACTIVE" checked>
                                            <label class="form-check-label" for="radiologistStatusActive">Active</label>
                                        </div>
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="radio" name="status" id="radiologistStatusInactive" value="INACTIVE" @if(old('status') == 'INACTIVE') checked @endif>
                                            <label class="form-check-label" for="radiologistStatusInactive">Inactive</label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Allowed Modalities -->
                            <div class="row" id="allowedModalities">
                                <div class="col-md-12 mb-3">
                                    <label class="form-label fw-bold">Allowed Modalities & Rates</label>
                                    <div class="mt-2">

                                        @foreach ($modalities as $key => $modality)
                                            <div class="row mb-2">
                                                <div class="col-md-7">
                                                    <div class="row">
                                                        <div class="col-md-8">
                                                            <div class="form-check">
                                                                <label class="form-check-label" for="modality">{{ $modality }}</label>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-4">
                                                            <div class="input-group input-group-sm">
                                                                <span class="input-group-text">BDT</span>
                                                                <input value="{{ old('rates.' . $key, 0) }}" type="number" class="form-control" name="rates[{{ $key }}]" placeholder="0" min="0" step="1" required>
                                                            </div>
                                                        </div>
                                                        <hr style="margin: 10px 0 0 0; padding: 0">
                                                    </div>
                                                </div>
                                            </div>
                                        @endforeach

                                    </div>
                                </div>
                            </div>

                            <div class="text-start mb-4">
                                <button type="submit" class="btn btn-primary" id="submitBtn">
                                    <i class="bi bi-person-plus me-1"></i><span id="submitText">Create Radiologist</span>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

@endsection

@section('footer')
<script>
    function previewSignature(input) {
        if (input.files && input.files[0]) {
            const reader = new FileReader();

            reader.onload = function(e) {
                document.getElementById('signatureImage').src = e.target.result;
                document.getElementById('signaturePreview').style.display = 'block';
            }

            reader.readAsDataURL(input.files[0]);
        }
    }

    $(document).ready(function() {
        $('#radiologistInternalNo').click(function() {
            $('#allowedModalities').css('display', 'block');
        });
        $('#radiologistInternalYes').click(function() {
            $('#allowedModalities').css('display', 'none');
        });
    });
</script>
@endsection
