@extends('master')

@section('title')
    Add User
@endsection

@section('header')

@endsection

@section('page_id')
    page-add-users
@endsection

@section('page_content')

    <!-- Main Content -->
    <div class="container-fluid py-4">
        <!-- Page Header -->
        <div class="row mb-4">
            <div class="col">
                <h1 class="h3 mb-0" id="pageTitle">Add User</h1>
                <p class="text-muted" id="pageDescription">Create new user account for system administrators and operators</p>
            </div>
            <div class="col-auto">
                <a href="{{ url('users') }}" class="btn btn-secondary">
                    <i class="bi bi-arrow-left me-1"></i>Back to Users
                </a>
            </div>
        </div>

        <!-- User Form -->
        <div class="row">
            <div class="col-12">
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="bi bi-shield-check me-2"></i>User Information
                        </h6>
                    </div>
                    <div class="card-body">
                        <form id="adminUserForm" method="POST" action="{{ url('users/add-user') }}" enctype="multipart/form-data">

                            @csrf

                            <div class="row">
                                <div class="col-md-12 mb-3">
                                    <label for="adminUserId" class="form-label fw-bold">User ID <span class="text-danger">*</span></label>
                                    <input value="{{ old('username') }}" type="text" placeholder="Write user ID without any spaces" class="form-control" id="adminUserId" name="username" required>
                                </div>
                                <div class="col-md-12 mb-3">
                                    <label for="adminFullName" class="form-label fw-bold">Full Name <span class="text-danger">*</span></label>
                                    <input value="{{ old('full_name') }}" type="text" class="form-control" id="adminFullName" name="name" required>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-12 mb-3">
                                    <label for="adminPassword" class="form-label fw-bold">Password <span class="text-danger">*</span></label>
                                    <input value="{{ old('password') }}" type="password" class="form-control" id="adminPassword" name="password" required>
                                </div>
                                <div class="col-md-12 mb-3">
                                    <label for="adminConfirmPassword" class="form-label fw-bold">Confirm Password <span class="text-danger">*</span></label>
                                    <input type="password" class="form-control" id="adminConfirmPassword" name="confirm_password" required>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-12 mb-3">
                                    <label for="adminMobileNo" class="form-label fw-bold">Mobile No <span class="text-danger">*</span></label>
                                    <input value="{{ old('mobile') }}" type="tel" class="form-control" id="adminMobileNo" name="mobile" required>
                                </div>
                                <div class="col-md-12 mb-3">
                                    <label for="adminUserRole" class="form-label fw-bold">User Role <span class="text-danger">*</span></label>
                                    <select class="form-control form-select" id="adminUserRole" name="type" required>
                                        <option value="">Select Role</option>
                                        <option value="ADMIN" @if(old('type') == 'ADMIN') selected @endif>Admin</option>
                                        <option value="MANAGER" @if(old('type') == 'MANAGER') selected @endif>Manager</option>
                                        <option value="SALES" @if(old('type') == 'SALES') selected @endif>Sales</option>
                                    </select>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-12 mb-3">
                                    <label for="tenantEmail" class="form-label fw-bold">Email</label>
                                    <input value="{{ old('email') }}" type="email" class="form-control" id="tenantEmail" name="email">
                                </div>

                                <div class="col-md-12 mb-3">
                                    <label class="form-label fw-bold">Status</label>
                                    <div class="mt-2">
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="radio" name="status" id="statusActive" value="ACTIVE" checked>
                                            <label class="form-check-label" for="statusActive">
                                                Active
                                            </label>
                                        </div>
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="radio" name="status" id="statusInactive" value="INACTIVE" @if(old('status') == 'INACTIVE') checked @endif>
                                            <label class="form-check-label" for="statusInactive">
                                                Inactive
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="text-start mb-4">
                                <button type="submit" class="btn btn-primary" id="submitBtn">
                                    <i class="bi bi-person-plus me-1"></i><span id="submitText">Create User</span>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

@endsection

@section('footer')
    <script>
        function previewSignature(input) {
            if (input.files && input.files[0]) {
                const reader = new FileReader();

                reader.onload = function(e) {
                    document.getElementById('signatureImage').src = e.target.result;
                    document.getElementById('signaturePreview').style.display = 'block';
                }

                reader.readAsDataURL(input.files[0]);
            }
        }
    </script>
@endsection
