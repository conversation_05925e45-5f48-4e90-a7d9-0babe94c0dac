@extends('master')

@section('title')
    Payment Transaction
@endsection

@section('header')

@endsection

@section('page_id')
    page-tenant-payment-transaction
@endsection

@section('page_content')

    <!-- Main Content -->
    <div class="container-fluid py-4">

        <!-- Pending Payments to Radiologists -->
        <div class="row">
            <div class="col-12">
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <h6 class="m-0 font-weight-bold text-success">
                                    <i class="bi bi-credit-card me-2"></i>Payments
                                </h6>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover table-striped default-table">
                                <thead class="table-dark">
                                <tr>
                                    <th>Payment ID</th>
                                    {{--<th>Name</th>
                                    <th>Type</th>--}}
                                    <th>Payment Month</th>
                                    <th>Reports Completed</th>
                                    <th>Total Amount</th>
                                    <th>Status</th>
                                    <th>Transaction Mode</th>
                                </tr>
                                </thead>
                                <tbody>

                                @forelse($transactions as $transaction)
                                    <tr>
                                        <td><strong>{{ $transaction->pid }}</strong></td>
                                       {{-- <td>{{ $transaction->user->name }}</td>
                                        <td>
                                            @if($transaction->user_type == \App\Constants\UserType::RADIOLOGIST)
                                                <span class="badge bg-success">Radiologist</span>
                                            @elseif($transaction->user_type == \App\Constants\UserType::TENANT)
                                                <span class="badge bg-info">Tenant</span>
                                            @endif
                                        </td>--}}
                                        <td>{{ date('F Y', strtotime($transaction->payment_month)) }}</td>
                                        <td>
                                            <a href="{{ url('payment/' . $transaction->id . '/details') }}" target="_blank"><span class="badge bg-success">{{ count(json_decode($transaction->lab_report_ids)) }}</span></a>
                                        </td>
                                        <td>BDT. {{ $transaction->amount }}</td>
                                        <td>
                                            @if($transaction->status == 'PENDING')
                                                <span class="badge bg-warning">Pending</span>
                                            @elseif($transaction->status == 'PAID')
                                                <span class="badge bg-success">Paid</span>
                                            @elseif($transaction->status == 'DUE')
                                                <span class="badge bg-danger">Due</span>
                                            @endif
                                        </td>
                                        <td>{{ $transaction->payment_method ?? '-' }}</td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="6" class="text-center">No records found.</td>
                                    </tr>
                                @endforelse
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <div class="d-flex justify-content-between align-items-center mt-3">
                            {{ $transactions->appends(request()->query())->links() }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

@endsection

@section('footer')

@endsection
