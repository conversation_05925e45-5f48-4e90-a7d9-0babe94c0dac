@extends('master')

@section('title')
    Payment Transaction
@endsection

@section('header')

@endsection

@section('page_id')
    page-user-payment-transaction
@endsection

@section('page_content')

    <!-- Main Content -->
    <div class="container-fluid py-4">

        <!-- Payment Filters -->
        <div class="row mb-4">
            <div class="col-12">
                <form action="{{ url('/payment') }}" method="GET">
                    <div class="card shadow-sm filter-block">
                        <div class="card-body py-3">
                            <div class="row align-items-center">
                                <div class="col-auto">
                                    <h6 class="mb-0 text-muted">
                                        <i class="bi bi-funnel me-2"></i>Filters:
                                    </h6>
                                </div>
                                <div class="col-md-1 col-sm-6">
                                    <div class="form-group">
                                        <label for="forYear" class="form-label small text-muted mb-1">Year</label>
                                        <select class="form-select form-select-sm" id="forYear" name="year">
                                            <option value="">All</option>
                                            @for ($year = date('Y'); $year >= 2025; $year--)
                                                <option value="{{ $year }}" {{ request('year') == $year ? 'selected' : '' }}>
                                                    {{ $year }}
                                                </option>
                                            @endfor
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-1 col-sm-6">
                                    <div class="form-group">
                                        <label for="forMonth" class="form-label small text-muted mb-1">Month</label>
                                        <select class="form-select form-select-sm" id="forMonth" name="month">
                                            <option value="" selected>All</option>
                                            <option value="01" @if(request('month') == '01') selected @endif>January</option>
                                            <option value="02" @if(request('month') == '02') selected @endif>February</option>
                                            <option value="03" @if(request('month') == '03') selected @endif>March</option>
                                            <option value="04" @if(request('month') == '04') selected @endif>April</option>
                                            <option value="05" @if(request('month') == '05') selected @endif>May</option>
                                            <option value="06" @if(request('month') == '06') selected @endif>June</option>
                                            <option value="07" @if(request('month') == '07') selected @endif>July</option>
                                            <option value="08" @if(request('month') == '08') selected @endif>August</option>
                                            <option value="09" @if(request('month') == '09') selected @endif>September</option>
                                            <option value="10" @if(request('month') == '10') selected @endif>October</option>
                                            <option value="11" @if(request('month') == '11') selected @endif>November</option>
                                            <option value="12" @if(request('month') == '12') selected @endif>December</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-3 col-sm-6">
                                    <div class="form-group">
                                        <label for="tenantFilter" class="form-label small text-muted mb-1">Hospital</label>
                                        <select class="form-select form-select-sm" id="tenantFilter" name="tenant">
                                            <option value="">All Hospitals</option>
                                            @foreach($tenants as $tenant)
                                                <option value="{{ $tenant->id }}" @if($tenant->id == request('tenant')) selected @endif>{{ $tenant->name }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-2 col-sm-6">
                                    <div class="form-group">
                                        <label for="radiologistFilter" class="form-label small text-muted mb-1">Radiologist</label>
                                        <select class="form-select form-select-sm" id="radiologistFilter" name="radiologist">
                                            <option value="">All Radiologists</option>
                                            @foreach($radiologists as $radiologist)
                                                <option value="{{ $radiologist->id }}" @if($radiologist->id == request('radiologist')) selected @endif>{{ $radiologist->name }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-2 col-sm-6">
                                    <div class="form-group">
                                        <label for="billingStatus" class="form-label small text-muted mb-1">Status</label>
                                        <select class="form-select form-select-sm" id="billingStatus" name="status">
                                            <option value="">All Status</option>
                                            <option value="PENDING" @if(request('status') == 'PENDING') selected @endif>Pending</option>
                                            <option value="DUE" @if(request('status') == 'DUE') selected @endif>Due</option>
                                            <option value="PAID" @if(request('status') == 'PAID') selected @endif>Paid</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-1 col-sm-6">
                                    <div class="form-group">
                                        <label class="form-label small text-muted mb-1">&nbsp;</label>
                                        <button type="submit" class="btn btn-primary btn-sm w-100">
                                            <i class="bi bi-search me-1"></i>Search
                                        </button>
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Pending Payments to Radiologists -->
        <div class="row">
            <div class="col-12">
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <h6 class="m-0 font-weight-bold text-success">
                                    <i class="bi bi-credit-card me-2"></i>Payments
                                </h6>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover table-striped default-table">
                                <thead class="table-dark">
                                <tr>
                                    <th>Payment ID</th>
                                    <th>Name</th>
                                    <th>Type</th>
                                    <th>Payment Month</th>
                                    <th>Reports Completed</th>
                                    <th>Total Amount</th>
                                    <th>Status</th>
                                    <th>Transaction Mode</th>
                                    <th>Action</th>
                                </tr>
                                </thead>
                                <tbody>

                                @forelse($transactions as $transaction)
                                    <tr>
                                        <td><strong>{{ $transaction->pid }}</strong></td>
                                        <td>{{ $transaction->user->name }}</td>
                                        <td>
                                            @if($transaction->user_type == \App\Constants\UserType::RADIOLOGIST)
                                                <span class="badge bg-success">Radiologist</span>
                                            @elseif($transaction->user_type == \App\Constants\UserType::TENANT)
                                                <span class="badge bg-info">Hospital</span>
                                            @endif
                                        </td>
                                        <td>{{ date('F Y', strtotime($transaction->payment_month)) }}</td>
                                        <td>
                                            <a href="{{ url('payment/' . $transaction->id . '/details') }}" target="_blank">
                                                <span class="badge bg-success"><i class="bi bi-layout-text-window-reverse me-1"></i> {{ count(json_decode($transaction->lab_report_ids)) }}</span>
                                            </a>
                                        </td>
                                        <td>BDT. {{ $transaction->amount }}</td>
                                        <td>
                                            @if($transaction->status == 'PENDING')
                                                <span class="badge bg-warning">Pending</span>
                                            @elseif($transaction->status == 'PAID')
                                                <span class="badge bg-success">Paid</span>
                                            @elseif($transaction->status == 'DUE')
                                                <span class="badge bg-danger">Due</span>
                                            @endif
                                        </td>
                                        <td>{{ $transaction->payment_method ?? '-' }}</td>
                                        <td>
                                            @if($transaction->status == 'DUE')
                                                <button onclick="settlePayment({{ $transaction->id }})" class="btn btn-danger btn-sm">Settle Now</button>
                                            @elseif($transaction->status == 'PAID')
                                                <span class="badge bg-success">Settled</span>
                                            @else
                                                <button onclick="confirmPendingToDue({{ $transaction->id }})" class="btn btn-warning btn-sm">Accept Now</button>
                                            @endif

                                            <button onclick="regenerateBill({{ $transaction->id }})" class="btn btn-success btn-sm"><i class="bi bi-arrow-clockwise me-1"></i> Regenerate</button>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="9" class="text-center">No records found.</td>
                                    </tr>
                                @endforelse
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <div class="d-flex justify-content-between align-items-center mt-3">
                            {{ $transactions->appends(request()->query())->links() }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Payment Confirmation Modal -->
    <div class="modal fade" id="paymentConfirmModal" tabindex="-1" aria-labelledby="paymentConfirmModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="paymentConfirmModalLabel">
                        <i class="bi bi-credit-card text-success me-2"></i>Confirm Payment
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="card border-0 bg-light">
                                <div class="card-body">
                                    <div class="form-group">
                                        <label for="paymentMethod" class="form-label">Select Payment Method</label>
                                        <select class="form-select" id="confirm_payment_method">
                                            <option value="BANK" selected>Bank Transfer</option>
                                            <option value="MFS">Mobile Banking</option>
                                            <option value="CASH">Cash</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>


                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="bi bi-x-circle me-1"></i>Cancel
                    </button>
                    <button type="button" class="btn btn-success" id="confirmPayment">
                        <i class="bi bi-credit-card me-1"></i>Confirm Payment
                    </button>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('footer')
    <script>
        function settlePayment(transactionId) {
            var modal = new bootstrap.Modal(document.getElementById('paymentConfirmModal'));
            modal.show();

            document.getElementById('confirmPayment').addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                var paymentMethod = document.getElementById('confirm_payment_method').value;

                modal.hide();
                // Make AJAX call
                $.ajax({
                    url: '{{ url('/payment/update-transaction-status') }}',
                    type: 'POST',
                    data: {
                        transaction_id: transactionId,
                        payment_method: paymentMethod,
                        old_status: 'DUE',
                        new_status: 'PAID',
                        _token: '{{ csrf_token() }}'
                    },
                    success: function(response) {
                        if (response.success) {
                            Swal.fire(
                                'Done!',
                                'Selected transaction has been settled.',
                                'success'
                            ).then(() => {
                                location.reload();
                            });
                        } else {
                            toastr.error(response.message);
                        }
                    },
                    error: function(xhr) {
                        toastr.error(xhr.responseJSON?.message || 'Something went wrong');
                    }
                });
            });
        }

        function confirmPendingToDue(transactionId) {
            Swal.fire({
                title: 'Are you sure?',
                text: "You are about to confirm the payment. This action cannot be undone.",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Yes, continue!',
                cancelButtonText: 'Cancel',
                reverseButtons: true
            }).then((result) => {
                if (result.isConfirmed) {
                    // Show loading state
                    Swal.fire({
                        title: 'Updating...',
                        text: 'Please wait while we update the status',
                        allowOutsideClick: false,
                        didOpen: () => {
                            Swal.showLoading();
                        }
                    });

                    // Make AJAX call
                    $.ajax({
                        url: '{{ url('/payment/update-transaction-status') }}',
                        type: 'POST',
                        data: {
                            transaction_id: transactionId,
                            old_status: 'PENDING',
                            new_status: 'DUE',
                            _token: '{{ csrf_token() }}'
                        },
                        success: function(response) {
                            if (response.success) {
                                Swal.fire(
                                    'Updated!',
                                    'The status has been updated to DUE.',
                                    'success'
                                ).then(() => {
                                    // Reload the page to reflect changes
                                    location.reload();
                                });
                            } else {
                                Swal.fire(
                                    'Error!',
                                    response.message || 'Failed to update status.',
                                    'error'
                                );
                            }
                        },
                        error: function(xhr) {
                            let errorMessage = 'An error occurred while updating the status.';
                            if (xhr.responseJSON && xhr.responseJSON.message) {
                                errorMessage = xhr.responseJSON.message;
                            }
                            Swal.fire(
                                'Error!',
                                errorMessage,
                                'error'
                            );
                        }
                    });
                }
            });
        }

        function regenerateBill(transactionId) {
            Swal.fire({
                title: 'Are you sure?',
                text: "You are about to regenerate the bill. This action cannot be undone.",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Yes, continue!',
                cancelButtonText: 'Cancel',
                reverseButtons: true
            }).then((result) => {
                if (result.isConfirmed) {
                    // Show loading state
                    Swal.fire({
                        title: 'Updating...',
                        text: 'Please wait while we update the status',
                        allowOutsideClick: false,
                        didOpen: () => {
                            Swal.showLoading();
                        }
                    });

                    // Make AJAX call
                    $.ajax({
                        url: '{{ url('/payment/bill-regenerate') }}',
                        type: 'POST',
                        data: {
                            transaction_id: transactionId,
                            _token: '{{ csrf_token() }}'
                        },
                        success: function(response) {
                            if (response.success) {
                                Swal.fire(
                                    'Done!',
                                    'The bill has been regenerated.',
                                    'success'
                                ).then(() => {
                                    location.reload();
                                });
                            } else {
                                Swal.fire(
                                    'Error!',
                                    response.message || 'Failed to regenerate the bill.',
                                    'error'
                                );
                            }
                        },
                        error: function(xhr) {
                            let errorMessage = 'An error occurred while regenerating the bill.';
                            if (xhr.responseJSON && xhr.responseJSON.message) {
                                errorMessage = xhr.responseJSON.message;
                            }
                            Swal.fire(
                                'Error!',
                                errorMessage,
                                'error'
                            );
                        }
                    });
                }
            });
        }
    </script>
@endsection
