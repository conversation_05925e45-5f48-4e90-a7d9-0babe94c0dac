@extends('master')

@section('title')
    Payment Transaction Details
@endsection

@section('header')

@endsection

@section('page_id')
    page-payment-transaction-details
@endsection

@section('page_content')

    <!-- Main Content -->
    <div class="container-fluid py-4">

        <!-- Pending Payments to Radiologists -->
        <div class="row">
            <div class="col-12">
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <h6 class="m-0 font-weight-bold text-success">
                                    <i class="bi bi-credit-card me-2"></i>Payments Details for {{ $transaction->pid }}
                                </h6>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover table-striped default-table">
                                <thead class="table-dark">
                                <tr>
                                    <th>Patient ID</th>
                                    <th>Patient Name</th>
                                    <th>Procedure</th>
                                    <th>Arrival Date/Time</th>
                                    <th>Hospital</th>
                                    <th>Radiologist</th>
                                    <th>Modality</th>
                                    <th>Order Time</th>
                                    <th>Completion Time</th>
                                    <th>Bill Amount</th>
                                </tr>
                                </thead>
                                <tbody>

                                @foreach($worklist as $item)
                                    <tr>
                                        <td><strong>{{ $item->patient_id }}</strong></td>
                                        <td>{{ $item->patient_name }}</td>
                                        <td>{{ $item->procedure }}</td>
                                        <td>{{ date('d M Y h:i A', strtotime($item->study_time)) }}</td>
                                        <td>{{ $item->tenant->name }}</td>
                                        <td>
                                            <i class="bi bi-person-check me-1"></i>{{ $item->completedBy->name }}
                                        </td>
                                        <td>{{ $item->modality }}</td>
                                        <td>{{ $item->assigned_at ? date('d M Y H:iA', strtotime($item->assigned_at)) : '-' }}</td>
                                        <td>{{ date('d M Y h:i A', strtotime($item->completed_at)) }}</td>
                                        <td>
                                            @if(auth()->user()->type == \App\Constants\UserType::RADIOLOGIST)
                                                BDT. {{ $item->radiologist_bill }}
                                            @elseif(auth()->user()->type == \App\Constants\UserType::TENANT)
                                                BDT. {{ $item->tenant_bill }}
                                            @else
                                                Radiologist: BDT. {{ $item->radiologist_bill }} <br>
                                                Hospital: BDT. {{ $item->tenant_bill }}
                                            @endif
                                        </td>
                                    </tr>
                                @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('footer')

@endsection
