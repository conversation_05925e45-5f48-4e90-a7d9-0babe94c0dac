<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>@yield('title') | {{ config('app.name') }}</title>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{{ asset('assets/img/favicon.ico') }}">

    <!-- Web App Manifest -->
    <link rel="manifest" href="{{ asset('assets/js/manifest.json') }}">

    <!-- Bootstrap 5.3.6 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.6/dist/css/bootstrap.min.css" rel="stylesheet" integrity="sha384-4Q6Gf2aSP4eDXB8Miphtr37CMZZQ5oXLH2yaXMJ2w8e2ZtHTl7GptT4jmndRuHDT" crossorigin="anonymous">

    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css">

    <!-- Custom CSS -->
    <link href="{{ url()->asset('assets/css/custom.css?v=31007') }}" rel="stylesheet">

    <!-- Select2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet" />

    <style>
        [v-cloak] {
            display: none;
        }
    </style>

    @yield('header')

</head>
<body class="@yield('page_id')">

    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark
        @if(auth()->user()->type == \App\Constants\UserType::ADMIN || auth()->user()->type == \App\Constants\UserType::MANAGER)
            bg-dark
        @elseif(auth()->user()->type == \App\Constants\UserType::TENANT)
            bg-primary
        @elseif(auth()->user()->type == \App\Constants\UserType::RADIOLOGIST)
            bg-success
        @else
            bg-primary
        @endif">
    <div class="container-fluid">

        @if (auth()->user()->type == \App\Constants\UserType::SALES)
        <a style="padding-left: 2px;" class="navbar-brand fw-bold" href="{{ url('/users') }}">
            {{-- {{ config('app.name') }} --}}
            <img src="{{ asset('assets/img/logo_white.svg') }}" alt="BMS" style="height: 40px; width: auto;">
        </a>
        @else
            <a style="padding-left: 2px;" class="navbar-brand fw-bold" href="{{ url('/worklist') }}">
                {{-- {{ config('app.name') }} --}}
                <img src="{{ asset('assets/img/logo_white.svg') }}" alt="BMS" style="height: 40px; width: auto;">
            </a>
        @endif

        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
            <span class="navbar-toggler-icon"></span>
        </button>

        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav me-auto">

                @if( auth()->user()->type != \App\Constants\UserType::SALES)
                <li class="nav-item">
                    <a class="nav-link {{ $page == config('nav.worklist') ? 'active' : '' }}" href="{{ url('/worklist') }}">
                        <i class="bi bi-list-task me-1"></i>Worklist
                    </a>
                </li>

                <li class="nav-item">
                    <a class="nav-link {{ $page == config('nav.archive') ? 'active' : '' }}" href="{{ url('/archive') }}">
                        <i class="bi bi-archive me-1"></i>Archive
                    </a>
                </li>
                @endif

                @if(auth()->user()->type == \App\Constants\UserType::ADMIN)
                <li class="nav-item">
                    <a class="nav-link {{ $page == config('nav.payment') ? 'active' : '' }}" href="{{ url('/payment') }}">
                        <i class="bi bi-bar-chart me-1"></i>Payment
                    </a>
                </li>
                @endif


                @if (auth()->user()->type == \App\Constants\UserType::ADMIN || auth()->user()->type == \App\Constants\UserType::MANAGER || auth()->user()->type == \App\Constants\UserType::SALES)
                <li class="nav-item">
                    <a class="nav-link {{ $page == config('nav.users') ? 'active' : '' }}" href="{{ url('/users') }}">
                        <i class="bi bi-people me-1"></i>Users
                    </a>
                </li>
                @endif
            </ul>

            @if (auth()->user()->type != \App\Constants\UserType::SALES)
            <ul id="vue-app" class="navbar-nav" v-cloak>
                <li class="nav-item" style="margin-right: 20px;" v-if="ja > 0">
                    <a href="/worklist" type="button" class="btn position-relative bg-white text-black">
                        New Report Assigned
                        <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">[[ ja ]]</span>
                    </a>
                </li>
                <li class="nav-item" style="margin-right: 20px;" v-if="jp > 0">
                    <a href="/worklist" type="button" class="btn position-relative bg-white text-black">
                        New Report Added
                        <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">[[ jp ]]</span>
                    </a>
                </li>
                <li class="nav-item" style="margin-right: 30px;" v-if="jc > 0">
                    <a href="/worklist" type="button" class="btn position-relative bg-white text-black">
                        Job Completed
                        <span class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">[[ jc ]]</span>
                    </a>
                </li>
            </ul>
            @endif

            <ul class="navbar-nav">
                <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="bi bi-person-circle me-1"></i> Settings
                    </a>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="{{ url('/profile') }}"><i class="bi bi-person me-2"></i>Profile</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="{{ url('auth/logout') }}"><i class="bi bi-box-arrow-right me-2"></i>Logout</a></li>
                    </ul>
                </li>
            </ul>
        </div>
    </div>
</nav>

    <!-- User Name Header -->
    <div class="w-100 text-center py-3 border-bottom">
        <span class="fw-bold fs-5">
            <i class="bi bi-person-circle me-2"></i>{{ Auth::user()->name }}
        </span>
    </div>

    <div class="container-fluid">
        @if ($errors->any())
            <div class="alert alert-danger alert-dismissible fade show text-center" style="margin: 20px 0 0 0">
                <h5 class="alert-heading">Something went wrong. Check the following:</h5>
                <ul class="mb-0">
                    @foreach ($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                </ul>
            </div>
        @endif
        @if (session('error'))
            <div class="alert alert-danger alert-dismissible fade show text-center" role="alert" style="margin: 20px 0 0 0">
                {{ session('error') }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        @endif
        @if (session('success'))
            <div class="alert alert-success alert-dismissible fade show text-center" role="alert" style="margin: 20px 0 0 0">
                {{ session('success') }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        @endif
    </div>
    <!-- Page Content -->
    @yield('page_content')

    @if((auth()->user()->type == \App\Constants\UserType::RADIOLOGIST || auth()->user()->type == \App\Constants\UserType::TENANT) && auth()->user()->is_accepted_pnp == 0)
        @include('privacy_policy')
    @endif

    <!-- Bootstrap 5.3.6 JS Bundle -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.6/dist/js/bootstrap.bundle.min.js" integrity="sha384-j1CDi7MgGQ12Z7Qab0qlWQ/Qqz24Gc6BM0thvEMVjHnfYGF0rmFCozFSxQBxwHKO" crossorigin="anonymous"></script>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- Initialize Bootstrap Tooltips -->
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        });
    </script>
    <!-- Select2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

    <!-- SweetAlert2 JS -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <!-- Toastr CSS & JS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>

    <script>
        toastr.options = {
            "closeButton": true,
            "progressBar": true,
            "positionClass": "toast-bottom-right",
            "timeOut": "5000",
            "extendedTimeOut": "2000"
        };
    </script>

    <!-- Custom JS -->
    <script src="{{ url()->asset('assets/js/custom.js') }}"></script>

    @yield('footer')

    <script src="https://js.pusher.com/8.2.0/pusher.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/vue/dist/vue.js"></script>

    @if(auth()->user()->type != \App\Constants\UserType::SALES)
    <script>
        // Enable pusher logging - don't include this in production
        //Pusher.logToConsole = true;

        var USERNAME    = "{{ auth()->user()->username }}";
        var USER_TYPE   = "{{ auth()->user()->type }}";
        var ENV         = "{{ config('app.env') }}";

        var pusher = new Pusher('{{ config('broadcasting.connections.pusher.key') }}', {
            cluster: '{{ config('broadcasting.connections.pusher.options.cluster') }}'
        });

        var channel = pusher.subscribe(ENV + '.bms');

        if(USER_TYPE === 'ADMIN' || USER_TYPE === 'MANAGER' || USER_TYPE === 'HOSPITAL') {
            channel.bind('new_job_placed', function (data) {
                if(USER_TYPE === 'ADMIN' || USER_TYPE === 'MANAGER' || data.tenant_username === USERNAME) {
                    toastr.success(data.message);
                    app.jp += 1;
                }
            });

            channel.bind('job_completed', function (data) {
                if(USER_TYPE === 'ADMIN' || USER_TYPE === 'MANAGER' || data.tenant_username === USERNAME) {
                    toastr.success(data.message);
                    app.jc += 1;
                }
            });
        }else if(USER_TYPE === 'RADIOLOGIST') {
            channel.bind('job_assigned.' + USERNAME, function (data) {
                toastr.success(data.message);
                app.ja += 1;
            });
        }

        // Vue application
        const app = new Vue({
            el: '#vue-app',
            delimiters: ['[[', ']]'],
            data: {
                jp: 0,
                jc: 0,
                ja: 0
            },
        });
    </script>
    @endif

</body>
</html>
