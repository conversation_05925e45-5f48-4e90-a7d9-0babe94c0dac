<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payment_transactions', function (Blueprint $table) {
            $table->id();
            $table->string('pid', 20)->unique();
            $table->unsignedBigInteger('user_id')->index('user_id');
            $table->string('user_type', 15);
            $table->unsignedBigInteger('amount');
            $table->string('status')->default('PENDING')->comment('PENDING/DUE/PAID');
            $table->string('payment_method',30)->nullable()->comment('BANK/MFS/CASH');
            $table->string('transaction_id',30)->nullable();
            $table->string('payment_month', 30)->nullable()->comment('Y-m');
            $table->text('lab_report_ids')->nullable();
            $table->timestamp('paid_at')->nullable();
            $table->unsignedBigInteger('approved_by')->nullable();
            $table->timestamp('approved_at')->nullable();
            $table->string('remarks')->nullable();
            $table->unique(['user_id', 'payment_month'], 'u_id_pm_unique');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payment_transactions');
    }
};
