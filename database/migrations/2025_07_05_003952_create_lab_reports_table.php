<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('lab_reports', function (Blueprint $table) {
            $table->id();
            $table->string('patient_id', 20);
            $table->string('patient_name', 100);
            $table->string('patient_age', 10)->nullable();
            $table->string('patient_gender', 10)->nullable();
            $table->string('procedure')->nullable();
            $table->text('procedure_history')->default('');
            $table->string('parent_procedure',10)->nullable();
            $table->text('clinical_history')->nullable();
            $table->unsignedBigInteger('tenant_id')->index('tenant_id');
            $table->string('modality')->nullable();
            $table->string('status', 10)->default('PENDING')->index('status')->comment('PENDING/UNASSIGNED/ASSIGNED/COMPLETED');
            $table->boolean('is_user_archived')->default(false);
            $table->boolean('is_tenant_archived')->default(false);
            $table->string('file_type')->default('DICOM')->comment('DICOM/OTHER');
            $table->text('file_links')->nullable();
            $table->text('report_feedback')->nullable();
            $table->timestamp('study_time')->nullable();
            $table->unsignedBigInteger('assigned_by')->nullable();
            $table->timestamp('assigned_at')->nullable();
            $table->unsignedBigInteger('completed_by')->nullable();
            $table->timestamp('completed_at')->nullable()->index('completed_at');
            $table->unsignedInteger('tenant_bill')->default(0);
            $table->unsignedInteger('radiologist_bill')->default(0);
            $table->unsignedBigInteger('doctor_id')->nullable();
            $table->timestamp('created_at')->nullable()->index('created_at');
            $table->timestamp('updated_at')->nullable();
            $table->index(['status', 'is_user_archived', 'created_at'], 'idx_status_userarchived_created');
            $table->index(['status', 'is_tenant_archived', 'tenant_id'], 'idx_status_tenantarchived_tenant');
            $table->index(['status', 'completed_by'], 'idx_status_completedby');
            $table->index(['tenant_id', 'is_tenant_archived'], 'idx_tenantid_archived');
            $table->index(['status', 'id'], 'idx_status_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('lab_reports');
    }
};
