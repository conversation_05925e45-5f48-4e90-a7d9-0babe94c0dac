<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('users', function (Blueprint $table) {
            $table->id();
            $table->string('username', 30)->unique('username');
            $table->string('password');
            $table->string('name', 100);
            $table->string('email', 100)->nullable();
            $table->string('mobile', 14)->nullable();
            $table->string('bmdc_number', 20)->nullable();
            $table->string('identity_info', 255)->nullable();
            $table->string('address')->nullable();
            $table->string('upazila', 60)->nullable();
            $table->string('district', 60)->nullable();
            $table->string('division', 60)->nullable();
            $table->string('fax', 30)->nullable();
            $table->string('contact_name', 50)->nullable();
            $table->string('contact_mobile', 14)->nullable();
            $table->string('modalities_n_rates')->nullable()->comment('JSON');//{"DX": 200, "CT": 100}
            $table->string('permissions')->nullable();
            $table->string('type', 15)->default('ADMIN')->index('type')->comment('ADMIN/MANAGER/TENANT/RADIOLOGIST');
            $table->string('status')->default('ACTIVE')->comment('ACTIVE/INACTIVE');
            $table->boolean('is_internal')->default(false);
            $table->string('signature')->nullable();
            $table->boolean('is_accepted_pnp')->default(false);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('users');
    }
};
