/* Custom CSS for BMS System */

/* General Styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Login Page Styles */
.vh-100 {
    min-height: 100vh;
}

.card {
    border-radius: 15px;
}

.card-body {
    border-radius: 15px;
}

.btn {
    border-radius: 8px;
}

/* .form-control {
    border-radius: 8px;
    border: 1px solid #e3e6f0;
    padding: 0.75rem 1rem;
} */

.form-control:focus {
    border-color: #4e73df;
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

.input-group-text {
    border-radius: 8px 0 0 8px;
    border: 1px solid #e3e6f0;
    background-color: #f8f9fc;
}

/* Dashboard Styles */
.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}

.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}

.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}

.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}

.text-xs {
    font-size: 0.7rem;
}

.font-weight-bold {
    font-weight: 700 !important;
}

.text-gray-800 {
    color: #5a5c69 !important;
}

.progress-sm {
    height: 0.5rem;
}

/* Navigation Styles */
.navbar-brand {
    font-size: 1.5rem;
}

.dropdown-menu {
    border-radius: 8px;
    border: none;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
}

.dropdown-item {
    padding: 0.5rem 1.5rem;
}

.dropdown-item:hover {
    background-color: #f8f9fc;
}

/* Card Styles */
.shadow {
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15) !important;
}

.card-header {
    background-color: #f8f9fc;
    border-bottom: 1px solid #e3e6f0;
    border-top-left-radius: 15px !important;
    border-top-right-radius: 15px !important;
}

/* Table Styles */
/* .table {
    color: #858796;
}

.table thead th {
    vertical-align: bottom;
    border-bottom: 2px solid #e3e6f0;
    background-color: #f8f9fc;
    color: #6e707e;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.85rem;
}

.table td {
    border-top: 1px solid #e3e6f0;
    padding: 1rem 0.75rem;
} */

/* Badge Styles */
.badge {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .card-body {
        padding: 2rem 1.5rem;
    }
    
    .navbar-brand {
        font-size: 1.25rem;
    }
    
    .h3 {
        font-size: 1.5rem;
    }
}

/* Animation for smooth transitions */
.btn, .form-control {
    transition: all 0.3s ease;
}

/* Custom column for 8 items in one row */
@media (min-width: 1200px) {
    .col-xl-1-5 {
        flex: 0 0 auto;
        width: 12.5%; /* 100% / 8 = 12.5% */
    }
}

/* Secondary Menu Styles */
.secondary-menu .btn {
    border-radius: 8px;
    padding: 0.5rem 0.75rem;
    font-weight: 500;
    border-width: 1px;
    font-size: 0.875rem;
}

.secondary-menu .btn:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.secondary-menu .btn i {
    font-size: 1rem;
}

/* Responsive text for secondary menu */
@media (max-width: 991.98px) {
    .secondary-menu .btn {
        padding: 0.5rem;
        text-align: center;
    }
}

/* Filter Block Styles */
.filter-block .form-select {
    border-radius: 6px;
    border: 1px solid #e3e6f0;
    font-size: 0.875rem;
}

.filter-block .form-select:focus {
    border-color: #4e73df;
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

.filter-block .form-label {
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.filter-block .btn {
    border-radius: 6px;
    font-weight: 500;
}

/* Responsive adjustments for filter block */
@media (max-width: 767.98px) {
    .filter-block .row > div {
        margin-bottom: 1rem;
    }

    .filter-block .col-auto {
        width: 100%;
        text-align: center;
        margin-bottom: 1rem;
    }
}

/* Worklist Table Styles */
.default-table {
    font-size: 0.875rem;
}

.default-table th {
    background-color: #343a40;
    color: white;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.75rem;
    letter-spacing: 0.5px;
    border: none;
    padding: 0.75rem 0.5rem;
    white-space: nowrap;
}

.default-table td {
    padding: 0.75rem 0.5rem;
    vertical-align: middle;
    border-color: #e3e6f0;
}

.default-table .btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
}

.default-table .x-ray-btn {
    padding: 2px 5px !important;
    border: 0px;
}

/* Status badges */
.badge {
    font-size: 0.7rem;
    padding: 0.35rem 0.65rem;
    font-weight: 500;
}

/* Responsive table adjustments */
@media (max-width: 991.98px) {
    .default-table {
        font-size: 0.8rem;
    }

    .default-table th,
    .default-table td {
        padding: 0.5rem 0.25rem;
    }
}

/* Pagination styling */
.pagination-sm .page-link {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
}

/* Checkbox styling */
.form-check-input {
    margin-top: 0;
}

.default-table .form-check-input {
    cursor: pointer;
}

.default-table th .form-check-input {
    margin: 0 auto;
}

/* Assignment controls styling */
.card-header .form-select {
    min-width: 180px;
}

.card-header .form-label {
    white-space: nowrap;
    color: #6c757d;
    font-weight: 600;
}

.card-header .btn {
    white-space: nowrap;
    min-width: auto;
}

.card-header .d-flex {
    flex-wrap: nowrap;
}

.card-header .text-nowrap {
    flex-shrink: 0;
}

/* Responsive adjustments for assignment controls */
@media (max-width: 767.98px) {
    .card-header .row > div {
        margin-bottom: 1rem;
        text-align: center;
    }

    .card-header .d-flex {
        justify-content: center;
    }

    .card-header .form-select {
        min-width: auto;
        width: 100%;
    }
}

/* Add Users Page Styles */
.btn-group .btn-check:checked + .btn {
    background-color: #0d6efd;
    border-color: #0d6efd;
    color: white;
}

.btn-group .btn {
    border-radius: 0;
}

.btn-group .btn:first-child {
    border-top-left-radius: 0.375rem;
    border-bottom-left-radius: 0.375rem;
}

.btn-group .btn:last-child {
    border-top-right-radius: 0.375rem;
    border-bottom-right-radius: 0.375rem;
}

/* Form validation styles */
.form-control.is-valid {
    border-color: #198754;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%23198754' d='m2.3 6.73.94-.94 1.44 1.44L7.4 4.5l.94.94L4.66 9.2z'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

.form-control.is-invalid {
    border-color: #dc3545;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath d='m5.8 4.6 1.4 1.4M7.2 4.6l-1.4 1.4'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

.form-select.is-valid {
    border-color: #198754;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 6 6 6 6-6'/%3e%3c/svg%3e"), url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%23198754' d='m2.3 6.73.94-.94 1.44 1.44L7.4 4.5l.94.94L4.66 9.2z'/%3e%3c/svg%3e");
    background-position: right 0.75rem center, center right 2.25rem;
    background-size: 16px 12px, calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

.form-select.is-invalid {
    border-color: #dc3545;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 6 6 6 6-6'/%3e%3c/svg%3e"), url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath d='m5.8 4.6 1.4 1.4M7.2 4.6l-1.4 1.4'/%3e%3c/svg%3e");
    background-position: right 0.75rem center, center right 2.25rem;
    background-size: 16px 12px, calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

/* Required field indicator */
.text-danger {
    color: #dc3545 !important;
}

/* Permissions section styling */
.permissions-section {
    background-color: #f8f9fc;
    border: 1px solid #e3e6f0;
    border-radius: 0.375rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.permissions-section h6 {
    font-weight: 600;
    margin-bottom: 1rem;
    color: #dc3545;
}

.form-check {
    padding-left: 1.5rem;
}

.form-check-inline {
    margin-right: 2rem;
    margin-bottom: 0.5rem;
}

.form-check-input {
    margin-top: 0.25rem;
}

.form-check-label {
    font-weight: 500;
    color: #495057;
    cursor: pointer;
    margin-left: 0.5rem;
}

/* Form transitions */
.card {
    transition: opacity 0.3s ease;
}

/* Responsive adjustments for user forms */
@media (max-width: 767.98px) {
    .btn-group {
        flex-direction: column;
    }

    .btn-group .btn {
        border-radius: 0.375rem !important;
        margin-bottom: 0.5rem;
    }

    .btn-group .btn:last-child {
        margin-bottom: 0;
    }
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
