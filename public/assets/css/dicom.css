/* Modern DICOM Viewer Styles */

/* File Link Button Styles - Using tool-btn base style */
.file-link-btn {
  background: none;
  border: 1px solid var(--border-color);
  color: var(--text-secondary);
  padding: 0.5rem;
  border-radius: 0.375rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  text-decoration: none;
  margin-bottom: 0.5rem;
  min-height: 2.5rem;
  font-size: 0.875rem;
}

.file-link-btn:hover {
  background-color: var(--border-color);
  color: var(--text-primary);
  text-decoration: none;
}

.file-link-btn.active {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transform: translateY(-1px);
}

.file-link-btn.active:hover {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transform: translateY(-1px);
}

.file-link-btn i {
  font-size: 1rem;
}

:root {
  /* Light theme colors */
  --primary-color: #2563eb;
  --primary-hover: #1d4ed8;
  --secondary-color: #64748b;
  --background-color: #ffffff;
  --surface-color: #f8fafc;
  --border-color: #e2e8f0;
  --text-primary: #1e293b;
  --text-secondary: #64748b;
  --text-muted: #94a3b8;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --error-color: #ef4444;
  --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

[data-theme="dark"] {
  /* Dark theme colors */
  --primary-color: #3b82f6;
  --primary-hover: #2563eb;
  --secondary-color: #6b7280;
  --background-color: #111827;
  --surface-color: #1f2937;
  --border-color: #374151;
  --text-primary: #f9fafb;
  --text-secondary: #d1d5db;
  --text-muted: #9ca3af;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --error-color: #ef4444;
  --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.3), 0 1px 2px 0 rgba(0, 0, 0, 0.2);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  background-color: var(--background-color);
  color: var(--text-primary);
  line-height: 1.6;
  overflow: hidden;
}

.app-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
}

/* Header */
.header {
  background-color: var(--surface-color);
  border-bottom: 1px solid var(--border-color);
  padding: 0.75rem 1.5rem;
  box-shadow: var(--shadow);
  z-index: 100;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 100%;
}

.app-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary-color);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.header-controls {
  display: flex;
  gap: 0.5rem;
}

.theme-toggle, .info-btn {
  background: none;
  border: 1px solid var(--border-color);
  color: var(--text-secondary);
  padding: 0.5rem;
  border-radius: 0.375rem;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
}

.theme-toggle:hover, .info-btn:hover {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

/* Main Content */
.main-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

/* Sidebar */
.sidebar {
  width: 320px;
  background-color: var(--surface-color);
  border-right: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  z-index: 1;
}

.sidebar h3 {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-secondary);
  margin-bottom: 0.75rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.upload-section, .file-list-section, .image-info-section {
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-color);
}

/* Upload Area */
.upload-area {
  border: 2px dashed var(--border-color);
  border-radius: 0.5rem;
  padding: 2rem 1rem;
  text-align: center;
  transition: all 0.2s;
  cursor: pointer;
}

.upload-area:hover, .upload-area.dragover {
  border-color: var(--primary-color);
}

.upload-content i {
  font-size: 2rem;
  color: var(--border-color);
  margin-bottom: 0.5rem;
}

.upload-content p {
  color: var(--text-secondary);
  margin-bottom: 0.5rem;
}

.upload-text {
  font-size: 0.875rem;
  color: var(--text-muted);
}

.upload-btn {
  background-color: var(--primary-color);
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 0.5rem;
}

.upload-btn:hover {
  background-color: var(--primary-hover);
}

/* File List */
.file-list {
  max-height: 200px;
  overflow-y: auto;
}

.no-files {
  text-align: center;
  color: var(--text-muted);
  padding: 2rem 1rem;
}

.no-files i {
  font-size: 2rem;
  margin-bottom: 0.5rem;
  display: block;
}

.file-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  border-radius: 0.375rem;
  cursor: pointer;
  transition: background-color 0.2s;
  margin-bottom: 0.5rem;
}

.file-item:hover {
  background-color: var(--border-color);
}

.file-item.active {
  background-color: var(--primary-color);
  color: white;
}

.file-icon {
  width: 2rem;
  height: 2rem;
  background-color: var(--primary-color);
  border-radius: 0.25rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 0.875rem;
}

.file-info {
  flex: 1;
  min-width: 0;
}

.file-name {
  font-weight: 500;
  font-size: 0.875rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.file-size {
  font-size: 0.75rem;
  color: var(--text-muted);
}

/* Image Info */
.image-info {
  font-size: 0.875rem;
}

.no-image {
  text-align: center;
  color: var(--text-muted);
  padding: 2rem 1rem;
}

.no-image i {
  font-size: 2rem;
  margin-bottom: 0.5rem;
  display: block;
}

.info-item {
  display: flex;
  justify-content: space-between;
  padding: 0.5rem 0;
  border-bottom: 1px solid var(--border-color);
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  font-weight: 500;
  color: var(--text-secondary);
}

.info-value {
  color: var(--text-primary);
  text-align: right;
  word-break: break-word;
}

.metadata-button-container {
  justify-content: center !important;
  padding: 0.75rem 0 !important;
  border-bottom: none !important;
}

.metadata-btn {
  background-color: var(--primary-color);
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  width: 100%;
  justify-content: center;
}

.metadata-btn:hover {
  background-color: var(--primary-hover);
}

.metadata-btn:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* Viewer Area */
.viewer-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* Toolbar */
.toolbar {
  background-color: var(--surface-color);
  border-bottom: 1px solid var(--border-color);
  padding: 0.75rem 1rem;
  display: flex;
  gap: 1rem;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
  position: relative;
  z-index: 50;
}

.tool-group {
  display: flex;
  gap: 0.25rem;
  border-right: 1px solid var(--border-color);
  padding-right: 1rem;
}

.tool-group:last-child {
  border-right: none;
  padding-right: 0;
}

.tool-btn {
  background: none;
  border: 1px solid var(--border-color);
  color: var(--text-secondary);
  padding: 0.5rem;
  border-radius: 0.375rem;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  position: relative;
}

.tool-btn:hover {
  background-color: var(--border-color);
  color: var(--text-primary);
}

.tool-btn.active {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transform: translateY(-1px);
}

.tool-btn.active:hover {
  background-color: var(--primary-color);
  opacity: 0.9;
}

/* Viewer Container */
.viewer-container {
  flex: 1;
  position: relative;
  background-color: var(--background-color);
  overflow: hidden;
}

.viewport-wrapper {
  width: 100%;
  height: 100%;
  position: relative;
}

.viewport {
  width: 100%;
  height: 100%;
  background-color: #000;
  position: relative;
}

/* Cursor styles for different tools */
.viewport.tool-windowlevel {
  cursor: crosshair;
}

.viewport.tool-pan {
  cursor: move;
}

.viewport.tool-zoom {
  cursor: zoom-in;
}

.viewport.tool-length,
.viewport.tool-angle {
  cursor: crosshair;
}

.viewport.tool-rectangle,
.viewport.tool-ellipse {
  cursor: crosshair;
}

.viewport.tool-probe {
  cursor: pointer;
}

/* Viewport Overlay */
.viewport-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 10;
}

.viewport-info {
  position: absolute;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  font-family: 'Courier New', monospace;
  max-width: 200px;
}

.viewport-info.top-left {
  top: 1rem;
  left: 1rem;
}

.viewport-info.top-right {
  top: 1rem;
  right: 1rem;
}

.viewport-info.bottom-left {
  bottom: 1rem;
  left: 1rem;
}

.viewport-info.bottom-right {
  bottom: 1rem;
  right: 1rem;
}

/* Frame Navigation for Multi-frame DICOM */
.frame-navigation {
  position: absolute;
  bottom: 1rem;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
  gap: 1rem;
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  font-family: 'Courier New', monospace;
  font-size: 0.875rem;
  z-index: 15;
}

.frame-btn {
  background: none;
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
}

.frame-btn:hover {
  background-color: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
}

.frame-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.frame-info {
  color: white;
  font-weight: 500;
  min-width: 100px;
  text-align: center;
}

/* Loading Indicator */
.loading-indicator {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: var(--text-secondary);
  z-index: 20;
  display: none;
}

.spinner {
  width: 3rem;
  height: 3rem;
  border: 3px solid var(--border-color);
  border-top: 3px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Welcome Screen */
.welcome-screen {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--surface-color);
  z-index: 15;
}

.welcome-content {
  text-align: center;
  max-width: 400px;
  padding: 2rem;
}

.welcome-icon {
  font-size: 4rem;
  color: var(--primary-color);
  margin-bottom: 1rem;
}

.welcome-content h2 {
  font-size: 1.5rem;
  margin-bottom: 0.5rem;
  color: var(--text-primary);
}

.welcome-content p {
  color: var(--text-secondary);
  margin-bottom: 2rem;
}

.welcome-upload-btn {
  background-color: var(--primary-color);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  cursor: pointer;
  font-weight: 500;
  font-size: 1rem;
  transition: background-color 0.2s;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.welcome-upload-btn:hover {
  background-color: var(--primary-hover);
}

/* Modals */
.modal {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  align-items: center;
  justify-content: center;
}

.modal.show {
  display: flex;
}

.modal-content {
  background-color: var(--surface-color);
  border-radius: 0.5rem;
  box-shadow: var(--shadow-lg);
  max-width: 90vw;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.modal-header {
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.modal-close {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 0.25rem;
  transition: color 0.2s;
}

.modal-close:hover {
  color: var(--text-primary);
}

.modal-body {
  padding: 1.5rem;
  overflow-y: auto;
  flex: 1;
}

/* Responsive Design */
@media (max-width: 768px) {
  .sidebar {
    width: 280px;
  }
  
  .toolbar {
    padding: 0.5rem;
    gap: 0.5rem;
  }
  
  .tool-group {
    gap: 0.125rem;
    padding-right: 0.5rem;
  }
  
  .tool-btn {
    width: 2rem;
    height: 2rem;
    font-size: 0.875rem;
  }
}

/* Metadata Modal Styles */
.metadata-content {
  max-height: 60vh;
  overflow-y: auto;
  font-family: 'Courier New', monospace;
  font-size: 0.875rem;
  padding-right: 1rem;
}

.metadata-content h4 {
  color: var(--primary-color);
  font-size: 1rem;
  font-weight: 600;
  margin: 1.5rem 0 0.75rem 0;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid var(--border-color);
}

.metadata-content h4:first-child {
  margin-top: 0;
}

.metadata-module {
  margin-bottom: 1rem;
}

.metadata-item {
  display: flex;
  justify-content: space-between;
  padding: 0.25rem 0;
  border-bottom: 1px solid var(--border-color);
}

.metadata-item:last-child {
  border-bottom: none;
}

.metadata-key {
  font-weight: 500;
  color: var(--text-secondary);
  min-width: 150px;
  flex-shrink: 0;
}

.metadata-value {
  color: var(--text-primary);
  text-align: right;
  word-break: break-word;
  margin-left: 1rem;
}

.no-metadata, .error {
  text-align: center;
  color: var(--text-muted);
  padding: 2rem;
  font-style: italic;
}

.error {
  color: var(--error-color);
}

/* About Modal Styles */
.about-content h4 {
  color: var(--primary-color);
  margin-bottom: 0.5rem;
}

.about-content h5 {
  color: var(--text-primary);
  margin: 1rem 0 0.5rem 0;
  font-size: 0.875rem;
  font-weight: 600;
}

.about-content ul {
  margin: 0.5rem 0 1rem 1.5rem;
  color: var(--text-secondary);
}

.about-content li {
  margin-bottom: 0.25rem;
}

.about-content kbd {
  background-color: var(--border-color);
  color: var(--text-primary);
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  font-family: monospace;
  border: 1px solid var(--border-color);
}

/* Enhanced drag and drop styles */
.upload-area.dragover {
  border-color: var(--primary-color);
  transform: scale(1.02);
}

/* Loading states */
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  color: var(--text-secondary);
}

.loading::before {
  content: '';
  width: 1rem;
  height: 1rem;
  border: 2px solid var(--border-color);
  border-top: 2px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 0.5rem;
}

/* Scrollbar styling */
.sidebar::-webkit-scrollbar,
.metadata-content::-webkit-scrollbar,
.file-list::-webkit-scrollbar {
  width: 6px;
}

.sidebar::-webkit-scrollbar-track,
.metadata-content::-webkit-scrollbar-track,
.file-list::-webkit-scrollbar-track {
  background: var(--surface-color);
}

.sidebar::-webkit-scrollbar-thumb,
.metadata-content::-webkit-scrollbar-thumb,
.file-list::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 3px;
}

.sidebar::-webkit-scrollbar-thumb:hover,
.metadata-content::-webkit-scrollbar-thumb:hover,
.file-list::-webkit-scrollbar-thumb:hover {
  background: var(--text-muted);
}

/* Tooltip styles */
.tool-btn[title]:hover::after {
  content: attr(title);
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  margin-top: 0.5rem;
  background-color: rgba(0, 0, 0, 0.9);
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  white-space: nowrap;
  z-index: 99999;
  pointer-events: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

/* Enhanced responsive design */
@media (max-width: 1024px) {
  .sidebar {
    width: 300px;
  }

  .upload-section, .file-list-section, .image-info-section {
    padding: 1rem;
  }
}

@media (max-width: 768px) {
  .main-content {
    flex-direction: column;
  }

  .sidebar {
    width: 100%;
    height: auto;
    max-height: 40vh;
    order: 2;
  }

  .viewer-area {
    order: 1;
    /* min-height: 60vh; */
  }

  .header-content {
    padding: 0 1rem;
  }

  .app-title {
    font-size: 1.25rem;
  }

  .viewport-info {
    font-size: 0.625rem;
    padding: 0.25rem;
    max-width: 150px;
  }

  .modal-content {
    margin: 1rem;
    max-width: calc(100vw - 2rem);
    max-height: calc(100vh - 2rem);
  }
}

/* Print styles */
@media print {
  .header, .sidebar, .toolbar {
    display: none !important;
  }

  .main-content {
    flex-direction: column;
  }

  .viewer-area {
    width: 100% !important;
    height: 100vh !important;
  }

  .viewport-overlay {
    display: none !important;
  }
}

/* Utility Classes */
.hidden {
  display: none !important;
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Focus styles for accessibility */
.tool-btn:focus,
.upload-btn:focus,
.welcome-upload-btn:focus,
.theme-toggle:focus,
.info-btn:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --border-color: #000000;
    --text-muted: #000000;
  }

  [data-theme="dark"] {
    --border-color: #ffffff;
    --text-muted: #ffffff;
  }
}
