/**
 * Modern DICOM Viewer using Cornerstone.js
 * Professional DICOM viewer with comprehensive tools and modern UI
 */

// Global variables
let element;
let currentImageId = null;
let currentImageIndex = 0;
let loadedFiles = [];
let currentTool = 'Wwwc';
let currentFrame = 0;
let totalFrames = 1;

// Check if all required dependencies are loaded
if (typeof cornerstone === 'undefined' ||
    typeof cornerstoneWADOImageLoader === 'undefined' ||
    typeof cornerstoneTools === 'undefined' ||
    typeof dicomParser === 'undefined' ||
    typeof cornerstoneMath === 'undefined') {
    console.error('Required Cornerstone dependencies are not loaded');
    document.addEventListener('DOMContentLoaded', function() {
        showError('Failed to load Cornerstone dependencies. Please refresh the page.');
    });
} else {
    // Initialize when DOM is ready
    document.addEventListener('DOMContentLoaded', initializeViewer);
}

/**
 * Initialize the DICOM viewer
 */
function initializeViewer() {
    try {
        // Initialize Cornerstone
        initializeCornerstone();

        // Setup UI event listeners
        setupEventListeners();

        // Initialize theme
        initializeTheme();

        // Check for file parameter in URL and auto-load
        checkAndLoadFileFromURL();

        // Hide preloader after 2 seconds to ensure everything is fully ready
        setTimeout(() => {
            if (typeof window.hidePreloader === 'function') {
                window.hidePreloader();
            }
        }, 2000);


    } catch (error) {
        console.error('Failed to initialize DICOM viewer:', error);
        showError('Failed to initialize DICOM viewer. Please refresh the page.');
    }
}

/**
 * Check URL parameters and auto-load DICOM file if specified
 */
function checkAndLoadFileFromURL() {
    const urlParams = new URLSearchParams(window.location.search);
    const filePath = urlParams.get('file');

    if (filePath) {
        // Auto-load the specified DICOM file
        loadDicomFileFromPath(filePath);
    }
}

/**
 * Load DICOM or Image file from server path
 */
function loadDicomFileFromPath(filePath) {
    try {
        // Create full URL for the file
        const fullUrl = atob(filePath);

        // Check if element is ready
        if (!element) {
            showError('Viewport not initialized. Please refresh the page.');
            return;
        }

        // Determine file type from URL extension
        const fileName = fullUrl.split('/').pop().toLowerCase();
        const isImageFile = /\.(jpg|jpeg|png|gif|bmp|webp)$/i.test(fileName);
        const isDicomFile = /\.(dcm|dicom)$/i.test(fileName) || !isImageFile;

        // Fetch the file as a blob first
        fetch(fullUrl, {
            method: 'GET',
            mode: 'cors',
            cache: 'no-cache',
            headers: {
                'Accept': isImageFile ? 'image/*' : 'application/dicom, application/octet-stream, */*'
            }
        })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status} - ${response.statusText}. URL: ${fullUrl}`);
                }
                return response.blob();
            })
            .then(blob => {
                // Create a File object from the blob
                const fileType = isImageFile ? blob.type || 'image/jpeg' : 'application/dicom';
                const file = new File([blob], fileName, { type: fileType });

                let imageId;

                if (isImageFile) {
                    // For regular images, use the full URL with http/https prefix
                    imageId = fullUrl;
                } else {
                    // For DICOM files, use the WADO image loader
                    if (!cornerstoneWADOImageLoader || !cornerstoneWADOImageLoader.wadouri) {
                        throw new Error('Cornerstone WADO Image Loader not properly initialized');
                    }
                    imageId = cornerstoneWADOImageLoader.wadouri.fileManager.add(file);
                }

                // Create file info
                const fileInfo = {
                    file: file,
                    name: fileName,
                    size: formatFileSize(file.size),
                    type: file.type,
                    imageId: imageId,
                    isImageFile: isImageFile
                };

                // Add to loaded files
                loadedFiles.push(fileInfo);

                // Load and display the image
                if (isImageFile) {
                    // For image files, use direct loading approach
                    return loadWebImage(imageId).then(image => {
                        // Display the image
                        cornerstone.displayImage(element, image);

                        // Update current image info
                        currentImageId = loadedFiles[loadedFiles.length - 1].imageId;
                        currentImageIndex = loadedFiles.length - 1;

                        // Store the image in fileInfo
                        loadedFiles[currentImageIndex].image = image;

                        return image;
                    });
                } else {
                    // For DICOM files, use Cornerstone's loader
                    return new Promise((resolve, reject) => {
                        try {
                            const loadPromise = cornerstone.loadImage(imageId);

                            if (!loadPromise || typeof loadPromise.then !== 'function') {
                                reject(new Error('Failed to create image loading promise for imageId: ' + imageId));
                                return;
                            }

                            loadPromise.then(image => {
                                if (!image) {
                                    reject(new Error('Image loading returned null or undefined'));
                                    return;
                                }

                                // Display the image
                                cornerstone.displayImage(element, image);

                                // Update current image info
                                currentImageId = loadedFiles[loadedFiles.length - 1].imageId;
                                currentImageIndex = loadedFiles.length - 1;

                                // Store the image in fileInfo
                                loadedFiles[currentImageIndex].image = image;

                                resolve(image);
                            }).catch(error => {
                                console.error('Error in DICOM load promise:', error);
                                reject(error);
                            });
                        } catch (error) {
                            console.error('Error calling cornerstone.loadImage for DICOM:', error);
                            reject(error);
                        }
                    });
                }
            })
            .then(image => {

                // Detect multi-frame DICOM
                let numberOfFrames = 1;

                // Check DICOM NumberOfFrames tag (0028,0008)
                if (image.data) {
                    try {
                        if (image.data.string) {
                            const framesTag = image.data.string('x00280008');
                            if (framesTag) {
                                const parsedFrames = parseInt(framesTag);
                                // Only accept if it's a valid multi-frame count (> 1 and <= 1000)
                                if (parsedFrames && parsedFrames > 1 && parsedFrames <= 1000) {
                                    numberOfFrames = parsedFrames;
                                }
                            }
                        }

                        if (numberOfFrames === 1 && image.data.uint16) {
                            const numberOfFramesTag = image.data.uint16('x00280008');
                            if (numberOfFramesTag && numberOfFramesTag > 1 && numberOfFramesTag <= 1000) {
                                numberOfFrames = numberOfFramesTag;
                            }
                        }
                    } catch (error) {
                        // Silent fallback to single frame
                    }
                }

                loadedFiles[currentImageIndex].numberOfFrames = numberOfFrames;
                totalFrames = numberOfFrames;
                currentFrame = 0;

                // Setup multi-frame navigation if needed
                if (numberOfFrames > 1) {
                    showFrameNavigation(numberOfFrames);
                } else {
                    hideFrameNavigation();
                }

                // Update UI
                addFileToList(loadedFiles[currentImageIndex]);
                updateFileListSelection(currentImageIndex);

                // Hide welcome screen after UI updates
                setTimeout(() => {
                    hideWelcomeScreen();
                }, 50);

                // Force a resize to ensure proper display
                setTimeout(() => {
                    cornerstone.resize(element);
                }, 100);
            })
            .catch(error => {
                let errorMessage = `Failed to load DICOM file: ${filePath}. Error: ${error.message}`;

                // Add specific guidance for common errors
                if (error.message.includes('Failed to fetch')) {
                    errorMessage += '\n\nPossible causes:\n• Network connectivity issue\n• CORS policy blocking the request\n• Invalid or expired file URL\n• S3 bucket permissions issue';
                } else if (error.message.includes('HTTP error')) {
                    errorMessage += '\n\nThe file server returned an error. Please check if the file exists and is accessible.';
                }

                showError(errorMessage);
                showWelcomeScreen(); // Show welcome screen again on error
            });

    } catch (error) {
        showError(`Failed to process DICOM file: ${filePath}`);
    }
}

/**
 * Custom image loader for regular images (JPG, PNG, etc.)
 */
function loadWebImage(imageId) {
    // Return a proper promise that Cornerstone expects
    const promise = new Promise((resolve, reject) => {
        const image = new Image();

        image.onload = function() {
            try {
                const canvas = document.createElement('canvas');
                const context = canvas.getContext('2d');

                canvas.width = image.width;
                canvas.height = image.height;
                context.drawImage(image, 0, 0);

                const imageData = context.getImageData(0, 0, canvas.width, canvas.height);
                const rgbaData = imageData.data;

                // Convert RGBA to grayscale using luminance formula
                const grayscaleData = new Uint16Array(canvas.width * canvas.height);
                for (let i = 0; i < grayscaleData.length; i++) {
                    const rgbaIndex = i * 4;
                    const r = rgbaData[rgbaIndex];
                    const g = rgbaData[rgbaIndex + 1];
                    const b = rgbaData[rgbaIndex + 2];

                    // Use standard luminance formula for better grayscale conversion
                    const grayscale = Math.round(0.299 * r + 0.587 * g + 0.114 * b);
                    grayscaleData[i] = grayscale;
                }

                const cornerstoneImage = {
                    imageId: imageId,
                    minPixelValue: 0,
                    maxPixelValue: 255,
                    slope: 1.0,
                    intercept: 0,
                    windowCenter: 127,
                    windowWidth: 255,
                    render: cornerstone.renderGrayscaleImage,
                    getPixelData: function() {
                        return grayscaleData;
                    },
                    rows: canvas.height,
                    columns: canvas.width,
                    height: canvas.height,
                    width: canvas.width,
                    color: false,
                    columnPixelSpacing: 1.0,
                    rowPixelSpacing: 1.0,
                    invert: false,
                    sizeInBytes: grayscaleData.length * 2 // 2 bytes per pixel for Uint16Array
                };

                resolve(cornerstoneImage);
            } catch (error) {
                reject(error);
            }
        };

        image.onerror = function(error) {
            reject(new Error('Failed to load image: ' + imageId));
        };

        // Set CORS if needed
        image.crossOrigin = 'anonymous';
        image.src = imageId;
    });

    // Add the promise property that Cornerstone might expect
    promise.imageId = imageId;

    return promise;
}

/**
 * Initialize Cornerstone and its dependencies
 */
function initializeCornerstone() {
    // Configure DICOM Image Loader
    cornerstoneWADOImageLoader.external.cornerstone = cornerstone;
    cornerstoneWADOImageLoader.external.dicomParser = dicomParser;

    // Register image loaders
    // Register WADO image loader for DICOM files
    if (cornerstoneWADOImageLoader && cornerstoneWADOImageLoader.wadouri) {
        cornerstone.registerImageLoader('wadouri', cornerstoneWADOImageLoader.wadouri.loadImage);
    }

    // Register custom image loader for regular images
    try {
        cornerstone.registerImageLoader('http', loadWebImage);
        cornerstone.registerImageLoader('https', loadWebImage);
        cornerstone.registerImageLoader('blob', loadWebImage);
    } catch (error) {
        // Silent error handling
    }

    // Configure Cornerstone Tools
    cornerstoneTools.external.cornerstone = cornerstone;
    cornerstoneTools.external.cornerstoneMath = cornerstoneMath;
    cornerstoneTools.external.Hammer = Hammer;

    // Initialize Cornerstone Tools
    cornerstoneTools.init();

    // Initialize web workers for DICOM parsing
    const config = {
        maxWebWorkers: navigator.hardwareConcurrency || 1,
        startWebWorkersOnDemand: true,
        taskConfiguration: {
            decodeTask: {
                initializeCodecsOnStartup: false,
                usePDFJS: false,
                strict: false,
            },
        },
    };

    cornerstoneWADOImageLoader.webWorkerManager.initialize(config);

    // Get the viewport element
    element = document.getElementById('viewport');

    // Enable the element for Cornerstone
    cornerstone.enable(element);

    // Setup tools
    setupTools();
}

/**
 * Setup Cornerstone tools
 */
function setupTools() {
    // Add tools to Cornerstone Tools
    cornerstoneTools.addTool(cornerstoneTools.WwwcTool);
    cornerstoneTools.addTool(cornerstoneTools.PanTool);
    cornerstoneTools.addTool(cornerstoneTools.ZoomTool);
    cornerstoneTools.addTool(cornerstoneTools.StackScrollMouseWheelTool);
    cornerstoneTools.addTool(cornerstoneTools.LengthTool);
    cornerstoneTools.addTool(cornerstoneTools.AngleTool);
    cornerstoneTools.addTool(cornerstoneTools.RectangleRoiTool);
    cornerstoneTools.addTool(cornerstoneTools.EllipticalRoiTool);
    cornerstoneTools.addTool(cornerstoneTools.ProbeTool);
    cornerstoneTools.addTool(cornerstoneTools.RotateTool);

    // Set initial tool states - no tools active by default
    cornerstoneTools.setToolActive('Pan', { mouseButtonMask: 2 });
    cornerstoneTools.setToolActive('Zoom', { mouseButtonMask: 4 });
    cornerstoneTools.setToolActive('StackScrollMouseWheel', {});

    // Set all tools to enabled but not active
    cornerstoneTools.setToolEnabled('Wwwc');
    cornerstoneTools.setToolEnabled('Length');
    cornerstoneTools.setToolEnabled('Angle');
    cornerstoneTools.setToolEnabled('RectangleRoi');
    cornerstoneTools.setToolEnabled('EllipticalRoi');
    cornerstoneTools.setToolEnabled('Probe');
    cornerstoneTools.setToolEnabled('Rotate');

    // No tool is active by default
    currentTool = null;
}

/**
 * Setup UI event listeners
 */
function setupEventListeners() {
    // File input elements (may not exist if upload section is removed)
    const fileInput = document.getElementById('fileInput');
    const uploadBtn = document.getElementById('uploadBtn');
    const uploadArea = document.getElementById('uploadArea');

    // File upload events (only if elements exist)
    if (uploadBtn && fileInput) {
        uploadBtn.addEventListener('click', () => {
            fileInput.click();
        });
    }

    if (fileInput) {
        fileInput.addEventListener('change', handleFileSelect);
    }

    // Drag and drop (only if upload area exists)
    if (uploadArea) {
        uploadArea.addEventListener('dragover', handleDragOver);
        uploadArea.addEventListener('dragleave', handleDragLeave);
        uploadArea.addEventListener('drop', handleFileDrop);
    }

    // Toolbar events
    document.querySelectorAll('.tool-btn[data-tool]').forEach(btn => {
        btn.addEventListener('click', () => setActiveTool(btn.dataset.tool));
    });

    document.querySelectorAll('.tool-btn[data-action]').forEach(btn => {
        btn.addEventListener('click', () => executeAction(btn.dataset.action));
    });

    // Theme toggle
    const themeToggle = document.getElementById('themeToggle');
    if (themeToggle) {
        themeToggle.addEventListener('click', toggleTheme);
    }

    // Info button
    const infoBtn = document.getElementById('infoBtn');
    if (infoBtn) {
        infoBtn.addEventListener('click', () => showModal('aboutModal'));
    }

    // Keyboard shortcuts
    document.addEventListener('keydown', handleKeyboardShortcuts);

    // Viewport events
    const viewportElement = document.getElementById('viewport');
    if (viewportElement) {
        viewportElement.addEventListener('cornerstoneimagerendered', updateViewportInfo);
        viewportElement.addEventListener('cornerstonenewimage', updateViewportInfo);

        // Add mouse wheel support for frame navigation
        viewportElement.addEventListener('wheel', handleMouseWheel);
    }
}

/**
 * Handle file selection
 */
function handleFileSelect(event) {
    const files = Array.from(event.target.files);

    if (files.length === 0) {
        return;
    }

    loadDicomFiles(files);
}

/**
 * Handle drag over event
 */
function handleDragOver(event) {
    event.preventDefault();
    event.currentTarget.classList.add('dragover');
}

/**
 * Handle drag leave event
 */
function handleDragLeave(event) {
    event.currentTarget.classList.remove('dragover');
}

/**
 * Handle file drop
 */
function handleFileDrop(event) {
    event.preventDefault();
    event.currentTarget.classList.remove('dragover');

    const files = Array.from(event.dataTransfer.files);
    const validFiles = files.filter(file => {
        const fileName = file.name.toLowerCase();
        const isDicom = fileName.endsWith('.dcm') || fileName.endsWith('.dicom') || file.type === 'application/dicom';
        const isImage = /\.(jpg|jpeg|png|gif|bmp|webp)$/i.test(fileName) || file.type.startsWith('image/');
        return isDicom || isImage;
    });

    if (validFiles.length === 0) {
        showError('Please drop valid DICOM files (.dcm, .dicom) or image files (.jpg, .png, .gif, .bmp, .webp)');
        return;
    }

    loadDicomFiles(validFiles);
}

/**
 * Load DICOM and Image files
 */
async function loadDicomFiles(files) {
    if (!files || files.length === 0) return;

    showLoading(true);
    hideWelcomeScreen();

    try {
        // Clear previous files
        loadedFiles = [];
        clearFileList();

        // Process each file
        for (let i = 0; i < files.length; i++) {
            const file = files[i];

            // Determine file type
            const fileName = file.name.toLowerCase();
            const isImageFile = /\.(jpg|jpeg|png|gif|bmp|webp)$/i.test(fileName);

            let imageId;

            if (isImageFile) {
                // For regular images, create a blob URL and use it as imageId
                const blobUrl = URL.createObjectURL(file);
                imageId = blobUrl;
            } else {
                // For DICOM files, use the WADO image loader
                if (!cornerstoneWADOImageLoader || !cornerstoneWADOImageLoader.wadouri) {
                    throw new Error('Cornerstone WADO Image Loader not properly initialized');
                }
                imageId = cornerstoneWADOImageLoader.wadouri.fileManager.add(file);
            }

            // Create file info without pre-loading (simpler approach)
            const fileInfo = {
                file: file,
                imageId: imageId,
                image: null, // Will be loaded when displayed
                name: file.name,
                size: formatFileSize(file.size),
                index: i,
                numberOfFrames: 1, // Will be detected when image is loaded
                isImageFile: isImageFile
            };

            loadedFiles.push(fileInfo);
            addFileToList(fileInfo);
        }

        // Load first image
        if (loadedFiles.length > 0) {
            await loadImage(loadedFiles[0]);
        }

    } catch (error) {
        console.error('Error loading DICOM files:', error);
        showError('Error loading DICOM files. Please ensure they are valid DICOM files.');
        showWelcomeScreen(); // Show welcome screen again on error
    } finally {
        showLoading(false);
    }
}

/**
 * Load and display a DICOM image
 */
async function loadImage(fileInfo) {
    try {
        showLoading(true);

        // Set current image
        currentImageId = fileInfo.imageId;
        currentImageIndex = fileInfo.index;

        // Load and display the image
        const loadPromise = cornerstone.loadImage(fileInfo.imageId);
        if (!loadPromise || typeof loadPromise.then !== 'function') {
            throw new Error('Failed to create image loading promise for imageId: ' + fileInfo.imageId);
        }

        const image = await loadPromise;
        if (!image) {
            throw new Error('Image loading returned null or undefined for imageId: ' + fileInfo.imageId);
        }

        cornerstone.displayImage(element, image);

        // Store the loaded image
        fileInfo.image = image;

        // Detect multi-frame DICOM
        let numberOfFrames = 1;

        if (image.data) {
            try {
                if (image.data.string) {
                    const framesTag = image.data.string('x00280008');
                    if (framesTag) {
                        const parsedFrames = parseInt(framesTag);
                        // Only accept if it's a valid multi-frame count (> 1 and <= 1000)
                        if (parsedFrames && parsedFrames > 1 && parsedFrames <= 1000) {
                            numberOfFrames = parsedFrames;
                        }
                    }
                }

                if (numberOfFrames === 1 && image.data.uint16) {
                    const numberOfFramesTag = image.data.uint16('x00280008');
                    if (numberOfFramesTag && numberOfFramesTag > 1 && numberOfFramesTag <= 1000) {
                        numberOfFrames = numberOfFramesTag;
                    }
                }
            } catch (error) {
                // Silent fallback to single frame
            }
        }

        fileInfo.numberOfFrames = numberOfFrames;
        totalFrames = numberOfFrames;
        currentFrame = 0;

        if (numberOfFrames > 1) {
            showFrameNavigation(numberOfFrames);
        } else {
            hideFrameNavigation();
        }

        // Update UI
        updateFileListSelection(fileInfo.index);
        updateViewportInfo();

    } catch (error) {
        console.error('Error loading image:', error);
        showError('Error loading image: ' + error.message);
        showWelcomeScreen(); // Show welcome screen again on error
    } finally {
        showLoading(false);
    }
}

/**
 * Set active tool
 */
function setActiveTool(toolName) {
    if (!element) return;

    // Deactivate all tools first
    cornerstoneTools.setToolPassive('Wwwc');
    cornerstoneTools.setToolPassive('Pan');
    cornerstoneTools.setToolPassive('Zoom');
    cornerstoneTools.setToolPassive('Length');
    cornerstoneTools.setToolPassive('Angle');
    cornerstoneTools.setToolPassive('RectangleRoi');
    cornerstoneTools.setToolPassive('EllipticalRoi');
    cornerstoneTools.setToolPassive('Probe');

    // Activate new tool
    currentTool = toolName;

    try {
        switch (toolName) {
            case 'WindowLevel':
                cornerstoneTools.setToolActive('Wwwc', { mouseButtonMask: 1 });
                break;
            case 'Pan':
                cornerstoneTools.setToolActive('Pan', { mouseButtonMask: 1 });
                break;
            case 'Zoom':
                cornerstoneTools.setToolActive('Zoom', { mouseButtonMask: 1 });
                break;
            case 'Length':
                cornerstoneTools.setToolActive('Length', { mouseButtonMask: 1 });
                break;
            case 'Angle':
                cornerstoneTools.setToolActive('Angle', { mouseButtonMask: 1 });
                break;
            case 'Rectangle':
                cornerstoneTools.setToolActive('RectangleRoi', { mouseButtonMask: 1 });
                break;
            case 'Ellipse':
                cornerstoneTools.setToolActive('EllipticalRoi', { mouseButtonMask: 1 });
                break;
            case 'Probe':
                cornerstoneTools.setToolActive('Probe', { mouseButtonMask: 1 });
                break;
        }
    } catch (error) {
        console.error('Error setting tool:', error);
    }

    // Update UI
    updateToolbarSelection(toolName);
}

/**
 * Execute viewport actions
 */
function executeAction(action) {
    if (!element || !currentImageId) return;

    try {
        switch (action) {
            case 'rotate-left':
                rotateViewport(-90);
                break;
            case 'rotate-right':
                rotateViewport(90);
                break;
            case 'flip-horizontal':
                flipViewport(true, false);
                break;
            case 'flip-vertical':
                flipViewport(false, true);
                break;
            case 'reset':
                resetViewport();
                break;
            case 'fit':
                fitToWindow();
                break;
            case 'invert':
                invertColors();
                break;
            case 'clear-annotations':
                clearAnnotations();
                break;
        }
    } catch (error) {
        console.error('Error executing action:', error);
    }
}
/**
 * Viewport manipulation functions
 */
function rotateViewport(degrees) {
    const viewport = cornerstone.getViewport(element);
    viewport.rotation = (viewport.rotation || 0) + degrees;
    cornerstone.setViewport(element, viewport);
}

function flipViewport(flipHorizontal, flipVertical) {
    const viewport = cornerstone.getViewport(element);

    if (flipHorizontal) {
        viewport.hflip = !viewport.hflip;
    }
    if (flipVertical) {
        viewport.vflip = !viewport.vflip;
    }

    cornerstone.setViewport(element, viewport);
}

function resetViewport() {
    cornerstone.reset(element);
    cornerstone.fitToWindow(element);
}

function fitToWindow() {
    cornerstone.fitToWindow(element);
}

function invertColors() {
    const viewport = cornerstone.getViewport(element);
    viewport.invert = !viewport.invert;
    cornerstone.setViewport(element, viewport);
}

function clearAnnotations() {
    const toolStateManager = cornerstoneTools.globalImageIdSpecificToolStateManager;
    toolStateManager.clear(element);
    cornerstone.updateImage(element);
}

/**
 * UI Update functions
 */
function updateToolbarSelection(toolName) {
    // Remove active class from all tool buttons
    document.querySelectorAll('.tool-btn').forEach(btn => {
        btn.classList.remove('active');
    });

    // Add active class to selected tool
    const activeBtn = document.querySelector(`[data-tool="${toolName}"]`);
    if (activeBtn) {
        activeBtn.classList.add('active');
    }

    // Update viewport cursor based on active tool
    const viewport = document.getElementById('viewport');
    if (viewport) {
        // Remove all tool cursor classes
        viewport.classList.remove('tool-windowlevel', 'tool-pan', 'tool-zoom', 'tool-length', 'tool-angle', 'tool-rectangle', 'tool-ellipse', 'tool-probe');

        // Add cursor class for current tool
        const toolClass = `tool-${toolName.toLowerCase()}`;
        viewport.classList.add(toolClass);
    }
}



function updateViewportInfo() {
    if (!element || !currentImageId) return;

    try {
        const viewport = cornerstone.getViewport(element);

        // Update patient info (if available)
        updatePatientInfo();

        // Update image info overlay
        const imageInfoOverlay = document.getElementById('imageInfoOverlay');
        if (imageInfoOverlay) {
            let imageInfo = `<div>Image: ${currentImageIndex + 1}/${loadedFiles.length}</div>`;

            // Add frame info for multi-frame DICOM
            if (totalFrames > 1) {
                imageInfo += `<div>Frame: ${currentFrame + 1}/${totalFrames}</div>`;
            }

            imageInfoOverlay.innerHTML = imageInfo;
        }

        // Update window/level info
        const windowLevelInfo = document.getElementById('windowLevelInfo');
        if (windowLevelInfo && viewport) {
            windowLevelInfo.innerHTML = `
                <div>W: ${Math.round(viewport.voi.windowWidth || 0)}</div>
                <div>L: ${Math.round(viewport.voi.windowCenter || 0)}</div>
            `;
        }

        // Update zoom info
        const zoomInfo = document.getElementById('zoomInfo');
        if (zoomInfo && viewport) {
            const zoom = (viewport.scale * 100).toFixed(0);
            zoomInfo.innerHTML = `<div>Zoom: ${zoom}%</div>`;
        }

    } catch (error) {
        console.error('Error updating viewport info:', error);
    }
}

function updatePatientInfo() {
    const patientInfoElement = document.getElementById('patientInfo');
    if (!patientInfoElement || !currentImageId) return;

    try {
        // Get current file info
        const currentFile = loadedFiles[currentImageIndex];

        // Get DICOM metadata
        let metadata = cornerstone.metaData.get('generalSeriesModule', currentImageId) || {};
        let patientModule = cornerstone.metaData.get('patientModule', currentImageId) || {};
        let studyModule = cornerstone.metaData.get('generalStudyModule', currentImageId) || {};

        // If cornerstone metadata is empty, extract directly from DICOM data
        if (Object.keys(patientModule).length === 0 && currentFile && currentFile.image && currentFile.image.data) {
            const data = currentFile.image.data;

            // Helper function to get DICOM value using multiple methods
            function getDicomValue(tag) {
                let value = null;

                // Method 1: elements object
                if (data.elements && data.elements[tag] && data.elements[tag].value !== undefined) {
                    value = data.elements[tag].value;
                }

                // Method 2: string method
                if (!value && data.string) {
                    try {
                        value = data.string(tag);
                    } catch (e) {
                        // Ignore errors
                    }
                }

                // Method 3: try without 'x' prefix
                if (!value && tag.startsWith('x')) {
                    const tagWithoutX = tag.substring(1);
                    if (data.elements && data.elements[tagWithoutX] && data.elements[tagWithoutX].value !== undefined) {
                        value = data.elements[tagWithoutX].value;
                    }
                }

                // Method 4: try with different formatting (group,element)
                if (!value && tag.startsWith('x') && tag.length === 9) {
                    const group = tag.substring(1, 5);
                    const element = tag.substring(5, 9);
                    const formattedTag = `(${group},${element})`;
                    if (data.elements && data.elements[formattedTag] && data.elements[formattedTag].value !== undefined) {
                        value = data.elements[formattedTag].value;
                    }
                }

                return value;
            }



            patientModule = {
                patientName: getDicomValue('x00100010'),
                patientId: getDicomValue('x00100020'),
            };

            studyModule = {
                studyDate: getDicomValue('x00080020'),
            };

            metadata = {
                modality: getDicomValue('x00080060'),
            };


        }

        const patientName = patientModule.patientName || 'Unknown';
        const patientId = patientModule.patientId || 'Unknown';
        const studyDate = formatDicomDate(studyModule.studyDate) || 'Unknown';
        const modality = metadata.modality || 'Unknown';

        patientInfoElement.innerHTML = `
            <div>${patientName}</div>
            <div>ID: ${patientId}</div>
            <div>${modality} - ${studyDate}</div>
        `;
    } catch (error) {
        console.error('Error updating patient info:', error);
        patientInfoElement.innerHTML = '<div>Patient Info Unavailable</div>';
    }
}

function addFileToList(fileInfo) {
    const fileList = document.getElementById('fileList');

    // Check if fileList exists (it might be hidden/removed)
    if (!fileList) {
        return;
    }

    // Remove "no files" message if it exists
    const noFiles = fileList.querySelector('.no-files');
    if (noFiles) {
        noFiles.remove();
    }

    const fileItem = document.createElement('div');
    fileItem.className = 'file-item';
    fileItem.dataset.index = fileInfo.index;

    fileItem.innerHTML = `
        <div class="file-icon">
            <i class="fas fa-file-medical"></i>
        </div>
        <div class="file-info">
            <div class="file-name" title="${fileInfo.name}">${fileInfo.name}</div>
            <div class="file-size">${fileInfo.size}</div>
        </div>
    `;

    fileItem.addEventListener('click', () => {
        loadImage(fileInfo);
    });

    fileList.appendChild(fileItem);
}

function updateFileListSelection(index) {
    // Check if file list exists before trying to update selection
    const fileList = document.getElementById('fileList');
    if (!fileList) {
        return;
    }

    document.querySelectorAll('.file-item').forEach(item => {
        item.classList.remove('active');
    });

    const activeItem = document.querySelector(`[data-index="${index}"]`);
    if (activeItem) {
        activeItem.classList.add('active');
    }
}

function clearFileList() {
    const fileList = document.getElementById('fileList');
    if (fileList) {
        fileList.innerHTML = '<div class="no-files"><i class="fas fa-folder-open"></i><p>No files loaded</p></div>';
    }
}
/**
 * Utility functions
 */
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Format DICOM date to readable format (21 Jun 2025)
 */
function formatDicomDate(dicomDate) {
    if (!dicomDate || dicomDate === 'N/A') return 'N/A';

    // DICOM dates are typically in YYYYMMDD format
    const dateStr = dicomDate.toString().replace(/\D/g, ''); // Remove non-digits

    if (dateStr.length === 8) {
        const year = dateStr.substring(0, 4);
        const month = dateStr.substring(4, 6);
        const day = dateStr.substring(6, 8);

        const monthNames = [
            'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
            'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
        ];

        const monthIndex = parseInt(month) - 1;
        if (monthIndex >= 0 && monthIndex < 12) {
            return `${parseInt(day)} ${monthNames[monthIndex]} ${year}`;
        }
    }

    return dicomDate; // Return original if can't parse
}

function showLoading(show) {
    const loadingIndicator = document.getElementById('loadingIndicator');
    if (loadingIndicator) {
        loadingIndicator.style.display = show ? 'block' : 'none';
    }
}

function hideWelcomeScreen() {
    const welcomeScreen = document.getElementById('welcomeScreen');
    if (welcomeScreen) {
        welcomeScreen.style.display = 'none';
    }
}

function showWelcomeScreen() {
    const welcomeScreen = document.getElementById('welcomeScreen');
    if (welcomeScreen) {
        welcomeScreen.style.display = 'flex';
    }
}

/**
 * Frame navigation functions for multi-frame DICOM
 */
function showFrameNavigation(numberOfFrames) {
    const frameNav = document.getElementById('frameNavigation');
    if (frameNav) {
        frameNav.style.display = 'flex';
        updateFrameInfo(1, numberOfFrames);
    }
}

function hideFrameNavigation() {
    const frameNav = document.getElementById('frameNavigation');
    if (frameNav) {
        frameNav.style.display = 'none';
    }
}



function updateFrameInfo(currentFrameNum, totalFrames) {
    const frameInfo = document.getElementById('frameInfo');
    if (frameInfo) {
        frameInfo.textContent = `Frame ${currentFrameNum} of ${totalFrames}`;
    }
}

function previousFrame() {
    if (!element || totalFrames <= 1) return;

    if (currentFrame > 0) {
        currentFrame--;

        try {
            // Try multiple methods for frame navigation
            const viewport = cornerstone.getViewport(element);

            // Method 1: Direct frame property
            if (viewport.frame !== undefined) {
                viewport.frame = currentFrame;
                cornerstone.setViewport(element, viewport);
            } else {
                // Method 2: Load specific frame using image loader
                const currentImageId = loadedFiles[currentImageIndex].imageId;
                const frameImageId = currentImageId + '?frame=' + currentFrame;

                const loadPromise = cornerstone.loadImage(frameImageId);
                if (loadPromise && typeof loadPromise.then === 'function') {
                    loadPromise.then(image => {
                        cornerstone.displayImage(element, image);
                    }).catch(() => {
                        // Method 3: Fallback - just update UI
                    });
                }
            }

            updateFrameInfo(currentFrame + 1, totalFrames);
            updateViewportInfo();
        } catch (error) {
            console.error('Error navigating to previous frame:', error);
        }
    }
}

function nextFrame() {
    if (!element || totalFrames <= 1) return;

    if (currentFrame < totalFrames - 1) {
        currentFrame++;

        try {
            // Try multiple methods for frame navigation
            const viewport = cornerstone.getViewport(element);

            // Method 1: Direct frame property
            if (viewport.frame !== undefined) {
                viewport.frame = currentFrame;
                cornerstone.setViewport(element, viewport);
            } else {
                // Method 2: Load specific frame using image loader
                const currentImageId = loadedFiles[currentImageIndex].imageId;
                const frameImageId = currentImageId + '?frame=' + currentFrame;

                const loadPromise = cornerstone.loadImage(frameImageId);
                if (loadPromise && typeof loadPromise.then === 'function') {
                    loadPromise.then(image => {
                        cornerstone.displayImage(element, image);
                    }).catch(() => {
                        // Method 3: Fallback - just update UI
                    });
                }
            }

            updateFrameInfo(currentFrame + 1, totalFrames);
            updateViewportInfo();
        } catch (error) {
            console.error('Error navigating to next frame:', error);
        }
    }
}

function goToFrame(frameNumber) {
    if (!element || totalFrames <= 1) return;

    const frameIndex = frameNumber - 1; // Convert to 0-based index
    if (frameIndex >= 0 && frameIndex < totalFrames) {
        try {
            currentFrame = frameIndex;

            // Try multiple methods for frame navigation
            const viewport = cornerstone.getViewport(element);

            // Method 1: Direct frame property
            if (viewport.frame !== undefined) {
                viewport.frame = frameIndex;
                cornerstone.setViewport(element, viewport);
            } else {
                // Method 2: Load specific frame using image loader
                const currentImageId = loadedFiles[currentImageIndex].imageId;
                const frameImageId = currentImageId + '?frame=' + frameIndex;

                const loadPromise = cornerstone.loadImage(frameImageId);
                if (loadPromise && typeof loadPromise.then === 'function') {
                    loadPromise.then(image => {
                        cornerstone.displayImage(element, image);
                    }).catch(() => {
                        // Method 3: Fallback - just update UI
                    });
                }
            }

            updateFrameInfo(frameNumber, totalFrames);
            updateViewportInfo();
        } catch (error) {
            console.error('Error jumping to frame:', error);
        }
    }
}

/**
 * Handle mouse wheel for frame navigation
 */
function handleMouseWheel(event) {
    if (totalFrames > 1) {
        event.preventDefault();

        if (event.deltaY > 0) {
            nextFrame();
        } else {
            previousFrame();
        }
    }
}



function showError(message) {
    // Simple error display - could be enhanced with a proper modal
    alert('Error: ' + message);
    console.error('DICOM Viewer Error:', message);
}

/**
 * Theme management
 */
function initializeTheme() {
    const savedTheme = localStorage.getItem('dicom-viewer-theme') || 'light';
    setTheme(savedTheme);
}

function toggleTheme() {
    const currentTheme = document.documentElement.getAttribute('data-theme') || 'light';
    const newTheme = currentTheme === 'light' ? 'dark' : 'light';
    setTheme(newTheme);
}

function setTheme(theme) {
    document.documentElement.setAttribute('data-theme', theme);
    localStorage.setItem('dicom-viewer-theme', theme);

    const themeToggle = document.getElementById('themeToggle');
    if (themeToggle) {
        const icon = themeToggle.querySelector('i');
        if (icon) {
            icon.className = theme === 'light' ? 'fas fa-moon' : 'fas fa-sun';
        }
    }
}

/**
 * Modal management
 */
function showModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.add('show');

        // Load content for specific modals
        if (modalId === 'metadataModal') {
            loadMetadata();
        }
    }
}

function closeModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.remove('show');
    }
}

function loadMetadata() {
    const metadataContent = document.getElementById('metadataContent');
    if (!metadataContent || !currentImageId) return;

    try {
        // Get current file info
        const currentFile = loadedFiles[currentImageIndex];
        const image = currentFile ? currentFile.image : null;

        // Get DICOM metadata
        let metadata = {};
        let patientModule = {};
        let studyModule = {};
        let seriesModule = {};

        try {
            // Try cornerstone metadata first
            metadata = cornerstone.metaData.get('generalImageModule', currentImageId) || {};
            patientModule = cornerstone.metaData.get('patientModule', currentImageId) || {};
            studyModule = cornerstone.metaData.get('generalStudyModule', currentImageId) || {};
            seriesModule = cornerstone.metaData.get('generalSeriesModule', currentImageId) || {};

            // If cornerstone metadata is empty, extract directly from DICOM data
            if (Object.keys(patientModule).length === 0 && image && image.data) {
                const data = image.data;

                // Helper function to get DICOM value using multiple methods
                function getDicomValue(tag) {
                    let value = null;

                    // Method 1: elements object
                    if (data.elements && data.elements[tag] && data.elements[tag].value !== undefined) {
                        value = data.elements[tag].value;
                    }

                    // Method 2: string method
                    if (!value && data.string) {
                        try {
                            value = data.string(tag);
                        } catch (e) {
                            // Ignore errors
                        }
                    }

                    // Method 3: try without 'x' prefix
                    if (!value && tag.startsWith('x')) {
                        const tagWithoutX = tag.substring(1);
                        if (data.elements && data.elements[tagWithoutX] && data.elements[tagWithoutX].value !== undefined) {
                            value = data.elements[tagWithoutX].value;
                        }
                    }

                    return value;
                }

                // Helper function to combine multiple DICOM values
                function getCombinedDicomValues(tags) {
                    const values = tags.map(tag => getDicomValue(tag)).filter(value => value && value.trim() !== '');
                    return values.length > 0 ? values.join(', ') : null;
                }

                // Extract patient information
                patientModule = {
                    patientName: getDicomValue('x00100010'),
                    patientId: getDicomValue('x00100020'),
                    patientBirthDate: getDicomValue('x00100030'),
                    patientSex: getDicomValue('x00100040'),
                    patientAge: getDicomValue('x00101010'),
                };

                // Extract study information
                studyModule = {
                    studyDate: getDicomValue('x00080020'),
                    studyTime: getDicomValue('x00080030'),
                    studyDescription: getDicomValue('x00081030'),
                    studyInstanceUID: getDicomValue('x0020000d'),
                    accessionNumber: getDicomValue('x00080050'),
                    referringPhysicianName: getDicomValue('x00080090'),
                    // Clinical History: combine multiple possible tags
                    clinicalHistory: getCombinedDicomValues([
                        'x00081080', // Admitting Diagnoses Description
                        'x00401002'  // Reason for the Requested Procedure
                    ]),
                };

                // Extract series information
                seriesModule = {
                    modality: getDicomValue('x00080060'),
                    seriesDescription: getDicomValue('x0008103e'),
                    seriesNumber: getDicomValue('x00200011'),
                    seriesDate: getDicomValue('x00080021'),
                    seriesTime: getDicomValue('x00080031'),
                    bodyPartExamined: getDicomValue('x00180015'),
                    // Procedure: combine multiple possible tags
                    procedureDescription: getCombinedDicomValues([
                        'x00321060', // Requested Procedure Description
                        'x00081032'  // Procedure Code Sequence
                    ]),
                };

                // Extract general image information
                metadata = {
                    institutionName: getDicomValue('x00080080'),
                    manufacturer: getDicomValue('x00080070'),
                    manufacturerModelName: getDicomValue('x00081090'),
                    softwareVersions: getDicomValue('x00181020'),
                    stationName: getDicomValue('x00081010'),
                    deviceSerialNumber: getDicomValue('x00181000'),
                    instanceNumber: getDicomValue('x00200013'),
                };


            }
        } catch (error) {
            // Error extracting metadata for modal
        }

        // Organize metadata into logical sections
        const sections = [
            {
                title: 'File Information',
                items: [
                    { label: 'File Name', value: currentFile ? currentFile.name : 'N/A' },
                    { label: 'File Size', value: currentFile ? currentFile.size : 'N/A' },
                ]
            },
            {
                title: 'Patient Information',
                items: [
                    { label: 'Patient Name', value: patientModule.patientName || 'N/A' },
                    { label: 'Patient ID', value: patientModule.patientId || 'N/A' },
                    { label: 'Patient Birth Date', value: formatDicomDate(patientModule.patientBirthDate) },
                    { label: 'Patient Sex', value: patientModule.patientSex || 'N/A' },
                    { label: 'Patient Age', value: patientModule.patientAge || 'N/A' },
                ]
            },
            {
                title: 'Study Information',
                items: [
                    { label: 'Study Date', value: formatDicomDate(studyModule.studyDate) },
                    { label: 'Study Time', value: studyModule.studyTime || 'N/A' },
                    { label: 'Study Description', value: studyModule.studyDescription || 'N/A' },
                    { label: 'Clinical History', value: studyModule.clinicalHistory || 'N/A' },
                    { label: 'Study Instance UID', value: studyModule.studyInstanceUID || 'N/A' },
                    { label: 'Accession Number', value: studyModule.accessionNumber || 'N/A' },
                    { label: 'Referring Physician', value: studyModule.referringPhysicianName || 'N/A' },
                ]
            },
            {
                title: 'Series Information',
                items: [
                    { label: 'Modality', value: seriesModule.modality || 'N/A' },
                    { label: 'Series Description', value: seriesModule.seriesDescription || 'N/A' },
                    { label: 'Series Number', value: seriesModule.seriesNumber || 'N/A' },
                    { label: 'Series Date', value: formatDicomDate(seriesModule.seriesDate) },
                    { label: 'Series Time', value: seriesModule.seriesTime || 'N/A' },
                    { label: 'Body Part Examined', value: seriesModule.bodyPartExamined || 'N/A' },
                    { label: 'Procedure Description', value: seriesModule.procedureDescription || 'N/A' },
                ]
            },
            {
                title: 'Image Properties',
                items: [
                    { label: 'Dimensions', value: image ? `${image.width} × ${image.height}` : 'N/A' },
                    { label: 'Pixel Spacing', value: image && image.rowPixelSpacing ? `${image.rowPixelSpacing.toFixed(2)} × ${image.columnPixelSpacing.toFixed(2)} mm` : 'N/A' },
                    { label: 'Slice Thickness', value: image && image.sliceThickness ? `${image.sliceThickness} mm` : 'N/A' },
                    { label: 'Window Center', value: image && image.windowCenter ? image.windowCenter : 'N/A' },
                    { label: 'Window Width', value: image && image.windowWidth ? image.windowWidth : 'N/A' },
                    { label: 'Instance Number', value: metadata.instanceNumber || 'N/A' },
                ]
            },
            {
                title: 'Equipment Information',
                items: [
                    { label: 'Institution', value: metadata.institutionName || 'N/A' },
                    { label: 'Manufacturer', value: metadata.manufacturer || 'N/A' },
                    { label: 'Model', value: metadata.manufacturerModelName || 'N/A' },
                    { label: 'Software Version', value: metadata.softwareVersions || 'N/A' },
                    { label: 'Station Name', value: metadata.stationName || 'N/A' },
                    { label: 'Device Serial Number', value: metadata.deviceSerialNumber || 'N/A' },
                ]
            }
        ];

        // Format metadata for display
        let html = '';
        sections.forEach(section => {
            html += `<h4>${section.title}</h4><div class="metadata-module">`;
            section.items.forEach(item => {
                html += `<div class="metadata-item">
                    <span class="metadata-key">${item.label}:</span>
                    <span class="metadata-value">${item.value}</span>
                </div>`;
            });
            html += '</div>';
        });

        if (html === '') {
            html = '<div class="no-metadata">No metadata available for this image.</div>';
        }

        metadataContent.innerHTML = html;

    } catch (error) {
        console.error('Error loading metadata:', error);
        metadataContent.innerHTML = '<div class="error">Error loading metadata.</div>';
    }
}

/**
 * Keyboard shortcuts
 */
function handleKeyboardShortcuts(event) {
    // Don't handle shortcuts if user is typing in an input
    if (event.target.tagName === 'INPUT' || event.target.tagName === 'TEXTAREA') {
        return;
    }

    const key = event.key.toLowerCase();
    const ctrlKey = event.ctrlKey || event.metaKey;

    switch (key) {
        case 'w':
            event.preventDefault();
            setActiveTool('WindowLevel');
            break;
        case 'p':
            if (!ctrlKey) {
                event.preventDefault();
                setActiveTool('Pan');
            } else {
                event.preventDefault();
                setActiveTool('Probe');
            }
            break;
        case 'z':
            event.preventDefault();
            setActiveTool('Zoom');
            break;
        case 's':
            event.preventDefault();
            setActiveTool('StackScroll');
            break;
        case 'l':
            event.preventDefault();
            setActiveTool('Length');
            break;
        case 'a':
            event.preventDefault();
            setActiveTool('Angle');
            break;
        case 'r':
            if (ctrlKey) {
                event.preventDefault();
                executeAction('reset');
            } else {
                event.preventDefault();
                setActiveTool('Rectangle');
            }
            break;
        case 'e':
            event.preventDefault();
            setActiveTool('Ellipse');
            break;
        case 'f':
            event.preventDefault();
            executeAction('fit');
            break;
        case 'i':
            event.preventDefault();
            executeAction('invert');
            break;
        case 'escape':
            // Close any open modals
            document.querySelectorAll('.modal.show').forEach(modal => {
                modal.classList.remove('show');
            });
            break;
        case 'arrowleft':
            event.preventDefault();
            previousFrame();
            break;
        case 'arrowright':
            event.preventDefault();
            nextFrame();
            break;
    }
}

// Close modals when clicking outside
document.addEventListener('click', function(event) {
    if (event.target.classList.contains('modal')) {
        event.target.classList.remove('show');
    }
});

// Prevent context menu on viewport
document.addEventListener('DOMContentLoaded', function() {
    const viewport = document.getElementById('viewport');
    if (viewport) {
        viewport.addEventListener('contextmenu', function(e) {
            e.preventDefault();
        });
    }
});


