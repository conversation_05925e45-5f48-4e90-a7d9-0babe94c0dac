<?php

namespace App\Http\Services;

use App\Constants\UserType;
use App\Models\PaymentTransaction;

class BillGeneration
{
    public function generateTenantBill($user, $completed_reports, $total_amount, $payment_month) {
        $paymentTransaction = new PaymentTransaction();
        $paymentTransaction->pid            = $this->generateUniqueId();
        $paymentTransaction->user_id        = $user->id;
        $paymentTransaction->user_type      = UserType::TENANT;
        $paymentTransaction->amount         = $total_amount;
        $paymentTransaction->status         = 'PENDING';
        $paymentTransaction->payment_month  = $payment_month;
        $paymentTransaction->lab_report_ids = json_encode($completed_reports->pluck('id')->toArray());
        $paymentTransaction->save();
    }

    public function generateRadiologistBill($user, $completed_reports, $total_amount, $payment_month) {
        $paymentTransaction = new PaymentTransaction();
        $paymentTransaction->pid            = $this->generateUniqueId();
        $paymentTransaction->user_id        = $user->id;
        $paymentTransaction->user_type      = UserType::RADIOLOGIST;
        $paymentTransaction->amount         = $total_amount;
        $paymentTransaction->status         = 'PENDING';
        $paymentTransaction->payment_month  = $payment_month;
        $paymentTransaction->lab_report_ids = json_encode($completed_reports->pluck('id')->toArray());
        $paymentTransaction->save();
    }

    private function generateUniqueId(): string{
        $microTime = microtime(true);
        $timeInt = (int) ($microTime * 1000000);

        $random = random_int(100000, 999999);
        $combined = $timeInt + $random;
        $base36 = base_convert($combined, 10, 36);

        return 'P' . strtoupper($base36);
    }
}
