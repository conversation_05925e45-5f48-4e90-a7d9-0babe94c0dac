<?php

namespace App\Http\Controllers;

use App\Constants\LabReportStatus;
use App\Constants\UserType;
use App\Events\JobAssignNotification;
use App\Events\JobCompletionNotification;
use App\Events\NewJobPlaceNotification;
use App\Http\Services\CacheService;
use App\Models\Assignee;
use App\Models\ClinicalHistory;
use App\Models\Doctor;
use App\Models\FeedbackTemplate;
use App\Models\LabReport;
use App\Models\Procedure;
use App\Models\User;
use DB;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class LabReportController extends Controller
{
    public function worklist(Request $request, CacheService $cacheService) {
        view()->share('page', config('nav.worklist'));

        $tenant = $request->input('tenant') ?? null;
        $radiologist = $request->input('radiologist') ?? null;
        $patient_id = $request->input('patient_id') ?? null;

        if(auth()->user()->type == UserType::ADMIN || auth()->user()->type == UserType::MANAGER) {
            $tenants = $cacheService->getTenants();
            $radiologists = $cacheService->getRadiologists();
            $procedures = $cacheService->getProcedures();

            $worklist = LabReport::query()->orderBy('id', 'desc')
                ->where('status', '!=',LabReportStatus::PENDING)
                ->where('is_user_archived', false);

            if($tenant) {
                $worklist = $worklist->where('tenant_id', $tenant);
            }
            if($radiologist) {
                $worklist = $worklist->where(function ($query) use ($radiologist) {
                    $query->whereHas('assignees', function ($q) use ($radiologist) {
                        $q->where('assignees.user_id', $radiologist);
                    })->orWhere('completed_by', $radiologist);
                });
            }
            if($patient_id) {
                $worklist = $worklist->where('patient_id', '=', $patient_id);
            }
            $worklist = $worklist->with('tenant','assignees')->paginate(50);

            return view('worklist.user', compact('tenants', 'radiologists', 'worklist','procedures'));
        }elseif(auth()->user()->type == UserType::TENANT) {
            $radiologists = $cacheService->getRadiologists();

            $worklist = LabReport::query()->orderBy('id', 'desc')
                ->where('tenant_id', auth()->user()->id)
                ->where('is_tenant_archived', false);

            if($radiologist) {
                $worklist = $worklist->where(function ($query) use ($radiologist) {
                    $query->whereHas('assignees', function ($q) use ($radiologist) {
                        $q->where('assignees.user_id', $radiologist);
                    })->orWhere('completed_by', $radiologist);
                });
            }
            if($patient_id) {
                $worklist = $worklist->where('patient_id', '=', $patient_id);
            }

            $worklist = $worklist->paginate(50);

            return view('worklist.tenant', compact('radiologists', 'worklist'));
        }elseif(auth()->user()->type == UserType::RADIOLOGIST) {
            $job_ids = DB::table('assignees')
                ->where('user_id', auth()->user()->id)
                ->pluck('lab_report_id')
                ->toArray();

            $worklist = LabReport::query()->orderBy('id', 'asc')
                ->where('status',LabReportStatus::ASSIGNED)
                ->whereIn('id', $job_ids);

            if($patient_id) {
                $worklist = $worklist->where('patient_id', '=', $patient_id);
            }

            $worklist = $worklist->paginate(50);

            return view('worklist.radiologist', compact('worklist'));
        }elseif(auth()->user()->type == UserType::SALES){
            return redirect()->route('users.index');
        }else{
            return 'Who are you?';
        }
    }

    public function addJobView(Request $request, CacheService $cacheService) {
        view()->share('page', config('nav.worklist'));

        $tenants = $cacheService->getTenants();
        $procedures = $cacheService->getProcedures();
        $doctors = DB::table('doctors')
            ->select('id', 'name')
            ->where('tenant_id', auth()->user()->id)
            ->get();

        $clinical_histories = DB::table('clinical_histories')
            ->select('id', 'history')
            ->where('tenant_id', auth()->user()->id)
            ->get();

        return view('worklist.add_job', compact('tenants', 'procedures','doctors','clinical_histories'));
    }

    public function addJob(Request $request) {
        try{
            $request->validate([
                'patient_id'                => 'required|string',
                'patient_name'              => 'required|string',
                'patient_age'               => 'required|string',
                'patient_gender'            => 'required|string',
                'arrival_time'              => 'required|string',
                'tenant'                    => 'required|integer',
                'procedure'                 => 'required|string',
                'clinical_history'          => 'required|string',
                'modality'                  => 'required|string',
                'dicomFile'                 => 'nullable|file|mimes:dcm,dicom',
                'otherFiles'                => 'nullable|array',
                'otherFiles.*'              => 'nullable|file|mimes:jpg,jpeg,png,pdf',
            ]);

            $labReport = new LabReport();

            if($request->hasFile('dicomFile')) {
                $labReport->file_links = $request->file('dicomFile')->store(config('filesystems.prefix') . '/archived');
                $labReport->file_type = 'DICOM';
            }else if($request->hasFile('otherFiles')) {
                $labReport->file_links = json_encode(array_map(function ($file) {
                    return $file->store(config('filesystems.prefix') . '/archived');
                }, $request->file('otherFiles')));
                $labReport->file_type = 'OTHER';
            }else{
                return redirect()->back()
                    ->with('error', 'No file selected.')
                    ->withInput();
            }

            $labReport->patient_id          = $request->patient_id;
            $labReport->patient_name        = $request->patient_name;
            $labReport->patient_age         = $request->patient_age;
            $labReport->patient_gender      = $request->patient_gender;
            $labReport->study_time          = date('Y-m-d H:i:s', strtotime($request->arrival_time));
            $labReport->tenant_id           = $request->tenant;
            $labReport->clinical_history    = $request->clinical_history;
            $labReport->procedure_history   = '';
            $labReport->modality            = $request->modality;
            $labReport->procedure           = $request->procedure;
            $labReport->status              = LabReportStatus::UNASSIGNED;

            $procedure = Procedure::where('name', $request->procedure)->first();
            $labReport->parent_procedure    = $procedure->parent;
            $labReport->doctor_id           = $request->doctor;

            $labReport->save();

            if(auth()->user()->id == $request->tenant) {
                $tenant_username = '';
            }else{
                $tenant = DB::table('users')->where('id', $request->tenant)->first();
                $tenant_username = $tenant->username;
            }

            event(new NewJobPlaceNotification(
                message: 'New Report has been added in Worklist',
                tenant_username: $tenant_username
            ));

            return response()->json([
                'success' => true,
                'message' => 'Job has been added successfully',
            ]);
        }catch (\Exception $ex){
            return response()->json([
                'success' => false,
                'message' => 'Error creating job: ' . $ex->getMessage(),
            ], 500);
        }
    }

    public function addDoctor(Request $request) {
        try {
            $request->validate([
                'name' =>   'required|string',
            ]);

            if(DB::table('doctors')->where('name', $request->name)->where('tenant_id', auth()->user()->id)->first()) {
                return response()->json([
                    'success' => false,
                    'message' => 'You already have a doctor with this name',
                ], 400);
            }

            $doctor = new Doctor();
            $doctor->name = $request->name;
            $doctor->tenant_id = auth()->user()->id;
            $doctor->save();

            return response()->json([
                'success'       => true,
                'doctor_id'     => $doctor->id,
                'doctor_name'   => $doctor->name,
                'message'       => 'Doctor has been added successfully',
            ]);

        }catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error while adding doctor: ' . $e->getMessage(),
            ], 500);
        }
    }

    public function addClinicalHistory(Request $request) {
        try {
            $request->validate([
                'history' =>   'required|string',
            ]);

            if(DB::table('clinical_histories')->where('history', $request->history)->where('tenant_id', auth()->user()->id)->first()) {
                return response()->json([
                    'success' => false,
                    'message' => 'You already have a clinical history with this text',
                ], 400);
            }

            $history = new ClinicalHistory();
            $history->history = $request->history;
            $history->tenant_id = auth()->user()->id;
            $history->save();

            return response()->json([
                'success'       => true,
                'history'       => $history->history,
                'message'       => 'Clinical history has been added successfully',
            ]);

        }catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error while adding clinical history: ' . $e->getMessage(),
            ], 500);
        }
    }

    public function dcmFileView(Request $request, $id, CacheService $cacheService) {
        $report = LabReport::findOrFail($id);

        $feedback_templates = $cacheService->getFeedbackTemplates(owner_id: auth()->user()->id);
        return view('worklist.dicom.view', compact('report', 'feedback_templates'));
    }

    public function fileView(Request $request, $id, CacheService $cacheService) {
        view()->share('page', config('nav.worklist'));

        $report = LabReport::findOrFail($id);
        $report->file_links = json_decode($report->file_links);

        $feedback_templates = $cacheService->getFeedbackTemplates(owner_id: auth()->user()->id);

        return view('worklist.file.view', compact('report', 'feedback_templates'));
    }

    public function print(Request $request, $id){
        view()->share('page', config('nav.worklist'));
        $report = LabReport::findOrFail($id);
        return view('worklist.print', compact('report'));
    }

    public function saveFeedback(Request $request, $id) {
        try {
            if(empty($request->report_content)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Report content is required',
                ], 400);
            }

            $request->validate([
                'report_content' => 'required|string',
            ]);

            $labReport = LabReport::findOrFail($id);

            $radiologist = User::find(auth()->user()->id);
            $tenant = User::where('id', $labReport->tenant_id)->first();

            $labReport->report_feedback     = $request->report_content;
            $labReport->status              = LabReportStatus::COMPLETED;
            $labReport->completed_by        = $radiologist->id;
            $labReport->completed_at        = now();
            $labReport->radiologist_bill    = $radiologist->is_internal == 1 ? 0 : ($radiologist->modalities_n_rates->{$labReport->parent_procedure} ?? 0);
            $labReport->tenant_bill         = $tenant?->modalities_n_rates->{$labReport->parent_procedure} ?? 0;
            $labReport->save();

            DB::table('assignees')->where('lab_report_id', $id)->delete();

            event(new JobCompletionNotification(
                message: 'A Report has been completed by ' . $radiologist->name. ' for tenant ' . $tenant->name,
                tenant_username: $tenant->username,
            ));

            return response()->json([
                'success' => true,
                'message' => 'Report saved successfully',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to save report',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function saveFeedbackTemplate(Request $request, CacheService $cacheService) {
        try{
            $request->validate([
                'template_name' => 'required|string',
                'description'   => 'required|string',
            ]);

            $exist = FeedbackTemplate::where('user_id', auth()->user()->id)->where('name', $request->template_name)->first();
            if($exist) {
                return response()->json([
                    'success' => false,
                    'message' => 'You already have a feedback template with this name',
                ], 400);
            }

            $feedbackTemplate = new FeedbackTemplate();
            $feedbackTemplate->name         = $request->template_name;
            $feedbackTemplate->description  = $request->description;
            $feedbackTemplate->user_id      = auth()->user()->id;
            $feedbackTemplate->save();

            $cacheService->getFeedbackTemplates(owner_id: auth()->user()->id, fresh: true);

            return response()->json([
                'success' => true,
                'message' => 'Feedback template saved successfully',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to save feedback template',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function updateClinicalHistory(Request $request)
    {
        $request->validate([
            'id' => 'required|exists:lab_reports,id',
            'clinical_history' => 'nullable|string|max:255'
        ]);

        try {
            $labReport = LabReport::findOrFail($request->id);
            $labReport->clinical_history = $request->clinical_history;

            $is_status_updated = false;

            //@todo need to rework next time. I don't like this. This is not good. working area: just remove single line comments
            //if(!empty($labReport->procedure)) {
            //    $labReport->status = LabReportStatus::UNASSIGNED;
            //    $is_status_updated = true;
            //}

            $labReport->save();

            return response()->json([
                'success' => true,
                'message' => 'Clinical history updated successfully',
                'is_status_updated' => $is_status_updated
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update clinical history',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function updateProcedure(Request $request)
    {
        $request->validate([
            'id' => 'required|exists:lab_reports,id',
            'procedure_id' => 'required|exists:procedures,id'
        ]);

        try {
            $labReport = LabReport::findOrFail($request->id);
            $procedure = Procedure::findOrFail($request->procedure_id);

            if($labReport->procedure_history == '') {
                $labReport->procedure_history = $labReport->procedure;
            }
            $labReport->procedure = $procedure->name;
            $labReport->parent_procedure = $procedure->parent;

            if($labReport->status == LabReportStatus::COMPLETED) {
                $radiologist = User::where('id', $labReport->completed_by)->first();
                $tenant = User::where('id', $labReport->tenant_id)->first();

                $labReport->radiologist_bill    = $radiologist->modalities_n_rates->{$labReport->parent_procedure} ?? 0;
                $labReport->tenant_bill         = $tenant?->modalities_n_rates->{$labReport->parent_procedure} ?? 0;
            }

            $labReport->save();

            return response()->json([
                'success' => true,
                'message' => 'Procedure updated successfully',
                'procedure_name' => $procedure->name,
                'procedure_history' => $labReport->procedure_history,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update procedure',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function merge(Request $request){
        $request->validate([
            'worklist_ids' => 'required|array',
            'worklist_ids.*' => 'exists:lab_reports,id'
        ]);

        try{

            $firstItem = LabReport::where('id', $request->worklist_ids[0])->where('status', LabReportStatus::UNASSIGNED)->firstOrFail();
            $file_links = [];
            foreach($request->worklist_ids as $key => $id) {
                $item = LabReport::findOrFail($id);
                if($item->status != LabReportStatus::UNASSIGNED || $item->tenant_id != $firstItem->tenant_id || $item->patient_id != $firstItem->patient_id) {
                    return response()->json([
                        'success' => false,
                        'message' => 'You can only merge unassigned reports'
                    ], 400);
                }
                if($key == 0) {
                    $firstItem->procedure_history = '';
                }

                if($item->procedure_history == '') {
                    $firstItem->procedure_history .= $item->procedure . '; ';
                }else{
                    $firstItem->procedure_history .= $item->procedure_history . '; ';
                }
                $firstItem->clinical_history .= $item->clinical_history . '; ';

                $decoded = json_decode($item->file_links, true);
                if (json_last_error() === JSON_ERROR_NONE && is_array($decoded)) {
                    $file_links = array_merge($file_links, $decoded);
                } else {
                    $file_links[] = $item->file_links;
                }
            }
            $firstItem->file_links = json_encode($file_links);

            DB::beginTransaction();
            $firstItem->save();
            foreach($request->worklist_ids as $key => $id) {
                if($key == 0) {
                    continue;
                }
                DB::table('lab_reports')
                    ->where('id', $id)
                    ->delete();
            }

            DB::commit();
            return response()->json([
                'success' => true,
                'message' => 'Successfully merged reports'
            ], 200);
        }catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Failed to assign radiologists: ' . $e->getMessage()
            ], 500);
        }

    }

    public function bulkAssignRadiologists(Request $request): JsonResponse
    {
        $request->validate([
            'worklist_ids' => 'required|array',
            'worklist_ids.*' => 'exists:lab_reports,id',
            'radiologist_ids' => 'required|array',
            'radiologist_ids.*' => 'exists:users,id,type,radiologist',
        ]);

        try {
            DB::beginTransaction();

            $worklistItems = LabReport::whereIn('id', $request->worklist_ids)->get();
            $radiologists = User::whereIn('id', $request->radiologist_ids)
                ->where('type', UserType::RADIOLOGIST)
                ->get();

            if (empty($radiologists)) {
                throw new \Exception('No valid radiologists selected');
            }

            $selectionErrorMessages = [];
            foreach($worklistItems as $item) {
                if($item->status == LabReportStatus::COMPLETED) {
                    $item->completed_by     = null;
                    $item->completed_at     = null;
                    $item->report_feedback  = null;
                    $item->radiologist_bill = 0;
                    $item->tenant_bill      = 0;
                }else if($item->status == LabReportStatus::ASSIGNED){
                    DB::table('assignees')
                        ->where('lab_report_id', $item->id)
                        ->delete();
                }
                foreach ($radiologists as $radiologist) {
                    Assignee::updateOrCreate([
                        'lab_report_id' => $item->id,
                        'user_id' => $radiologist->id,
                    ]);

                    event(new JobAssignNotification(
                        message: 'A Report has been assigned to you by ' . auth()->user()->name,
                        radiologist_username: $radiologist->username
                    ));
                }
                $item->status = LabReportStatus::ASSIGNED;
                $item->assigned_by      = auth()->user()->id;
                $item->assigned_at      = now();
                $item->save();
            }

            DB::commit();

            return response()->json([
                'success'                   => true,
                'message'                   => 'Successfully assigned radiologists to selected items',
                'total_worklist_count'      => $worklistItems->count(),
                'success_worklist_count'    => $worklistItems->count() - count($selectionErrorMessages),
                'radiologist_count'         => count($radiologists),
                'selection_error_messages'  => count($selectionErrorMessages) > 0 ? json_encode($selectionErrorMessages) : ''
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Failed to assign radiologists: ' . $e->getMessage()
            ], 500);
        }
    }

    public function bulkArchive(Request $request): JsonResponse
    {
        $request->validate([
            'worklist_ids' => 'required|array',
            'worklist_ids.*' => 'exists:lab_reports,id',
        ]);

        try {
            $worklistItemIds = DB::table('lab_reports')->select('id')->whereIn('id', $request->worklist_ids)->where('status', LabReportStatus::COMPLETED)->pluck('id')->toArray();
            if(auth()->user()->type == UserType::ADMIN || auth()->user()->type == UserType::MANAGER) {
                DB::table('lab_reports')
                    ->whereIn('id', $worklistItemIds)
                    ->update([
                        'is_user_archived' => 1,
                    ]);
            }else if(auth()->user()->type == UserType::TENANT) {
                DB::table('lab_reports')
                    ->whereIn('id', $worklistItemIds)
                    ->update([
                        'is_tenant_archived' => 1,
                    ]);
            }
            return response()->json([
                'success'                   => true,
                'message'                   => 'Successfully archived selected items',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to assign radiologists: ' . $e->getMessage()
            ], 500);
        }
    }

    private function generateRandomCode(int $length = 4) {
        $characters = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $result = '';
        for ($i = 0; $i < $length; $i++) {
            $index = random_int(0, strlen($characters) - 1);
            $result .= $characters[$index];
        }
        return $result;
    }
}
