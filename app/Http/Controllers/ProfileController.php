<?php

namespace App\Http\Controllers;

use App\Constants\UserType;
use App\Http\Services\CacheService;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Hash;

class ProfileController extends Controller
{
    public function logout(Request $request) {
        Auth::logout();
        return redirect('/login');
    }

    public function profile(Request $request, CacheService $cacheService) {
        view()->share('page', 0);
        $modalities = $cacheService->getModalityTemplates();
        $user = User::find(auth()->user()->id);
        if(auth()->user()->type == UserType::ADMIN || auth()->user()->type == UserType::MANAGER || auth()->user()->type == UserType::SALES) {
            return view('profile.user', compact('user'));
        }else if(auth()->user()->type == UserType::TENANT) {
            return view('profile.tenant', compact('user', 'modalities'));
        }else if(auth()->user()->type == UserType::RADIOLOGIST) {
            return view('profile.radiologist', compact('user', 'modalities'));
        }else{
            return redirect('/');
        }
    }

    public function profileUpdate(Request $request) {
        try{
            $user = User::find(auth()->user()->id);

            if($request->has('mobile')) {
                $user->mobile = $request->mobile;
            }

            if($request->has('email')) {
                $user->email = $request->email;
            }

            if($request->has('password') && $request->filled('password') && strlen($request->password) >= 6 && $request->password == $request->confirm_password) {
                $user->password = Hash::make($request->password);
            }

            if($request->hasFile('signature')) {
                $user->signature = $request->file('signature')->store(config('filesystems.prefix') . '/signatures');
            }

            if($request->has('bmdc_number')){
                $user->bmdc_number = $request->bmdc_number;
            }

            if($request->has('address')){
                $user->address = $request->address;
            }

            if($request->has('fax')){
                $user->fax = $request->fax;
            }

            if($request->has('contact_name')){
                $user->contact_name = $request->contact_name;
            }

            if($request->has('contact_mobile')){
                $user->contact_mobile = $request->contact_mobile;
            }

            $user->save();

            return redirect()->back()
                ->with('success', 'Profile updated successfully.');
        }catch (\Exception $ex){
            return redirect()->back()
                ->with('error', 'Error updating profile: ' . $ex->getMessage())
                ->withInput();
        }
    }
}
