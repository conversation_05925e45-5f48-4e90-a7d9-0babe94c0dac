<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class LoginController extends Controller
{
    public function setLoginView(Request $request){
        if(Auth::check()) {
            return redirect('/');
        }

        return view('login');
    }

    public function getLogin(Request $request) {
        $request->validate([
            'user_id'       => 'required',
            'password'      => 'required',
        ]);


        if(Auth::attempt(['username' => $request->user_id, 'password' => $request->password, 'status' => 'ACTIVE'])) {
            return redirect('/');
        }

        return redirect('/login')->withErrors(['old_user_id'=> $request->user_id]);
    }
}
