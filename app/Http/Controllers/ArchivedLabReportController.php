<?php

namespace App\Http\Controllers;

use App\Constants\LabReportStatus;
use App\Constants\UserType;
use App\Http\Services\CacheService;
use App\Models\LabReport;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class ArchivedLabReportController extends Controller
{
    public function archivedList(Request $request, CacheService $cacheService) {
        view()->share('page', config('nav.archive'));

        $tenant = $request->input('tenant') ?? null;
        $radiologist = $request->input('radiologist') ?? null;
        $patient_id = $request->input('patient_id') ?? null;

        if(auth()->user()->type == UserType::ADMIN || auth()->user()->type == UserType::MANAGER) {
            $date_from = $request->input('date_from') ?? null;
            $date_to = $request->input('date_to') ?? null;

            $tenants = $cacheService->getTenants();
            $radiologists = $cacheService->getRadiologists();
            $procedures = $cacheService->getProcedures();
            $billGen = [];
            $summary = null;

            $worklist = LabReport::query()->orderBy('id', 'desc')
                ->where('status',LabReportStatus::COMPLETED)
                ->where('is_user_archived', true);

            if($date_from && $date_to) {
                $worklist = $worklist->where('created_at', '>=',$date_from . ' 00:00:00')
                    ->where('created_at', '<=',$date_to . ' 23:59:59');
            }

            if($tenant) {
                $worklist = $worklist->where('tenant_id', $tenant);
            }
            if($radiologist) {
                $worklist = $worklist->where('completed_by', $radiologist);
            }

            if ($date_from && $date_to) {
                $from = Carbon::parse($date_from);
                $to = Carbon::parse($date_to);

                if ($from->format('Y-m') === $to->format('Y-m') && ($tenant && !$radiologist) || (!$tenant && $radiologist)) {
                    $billGen['status']          = true;
                    $billGen['user_id']         = $tenant ?: $radiologist;
                    $billGen['user_type']       = $tenant ? UserType::TENANT : UserType::RADIOLOGIST;
                    $billGen['payment_month']  = $from->format('Y-m');
                }
                if ($from->format('Y-m') === $to->format('Y-m')) {
                    $cloneWorklist = clone $worklist;
                    $cloneWorklist = $cloneWorklist->get();

                    $summary = (object) [];
                    $summary->total_jobs        = $cloneWorklist->count();
                    $summary->tenant_bill       = $cloneWorklist->sum('tenant_bill');
                    $summary->radiologist_bill  = $cloneWorklist->sum('radiologist_bill');
                }
            }

            if($patient_id) {
                $worklist = $worklist->where('patient_id', '=', $patient_id);
            }

            $worklist = $worklist->with('completedBy','tenant')->paginate(50);

            return view('archive.user', compact('tenants', 'radiologists', 'worklist', 'procedures', 'billGen', 'summary'));
        }elseif(auth()->user()->type == UserType::TENANT) {
            $radiologists = $cacheService->getRadiologists();

            $worklist = LabReport::query()->orderBy('id', 'desc')
                ->where('tenant_id', auth()->user()->id)
                ->where('status',LabReportStatus::COMPLETED)
                ->where('is_tenant_archived', true);

            if($radiologist) {
                $worklist = $worklist->where('completed_by', $radiologist);
            }
            if($patient_id) {
                $worklist = $worklist->where('patient_id', '=', $patient_id);
            }

            $worklist = $worklist->with('completedBy')->paginate(50);

            return view('archive.tenant', compact('radiologists', 'worklist'));
        }elseif(auth()->user()->type == UserType::RADIOLOGIST){
            $worklist = LabReport::query()->orderBy('id', 'desc')
                ->where('completed_by', auth()->user()->id)
                ->where('status',LabReportStatus::COMPLETED);

            if($patient_id) {
                $worklist = $worklist->where('patient_id', '=', $patient_id);
            }

            $worklist = $worklist->paginate(50);

            return view('archive.radiologist', compact('worklist'));
        }else{
            return redirect('/');
        }
    }

    public function unarchive(Request $request): JsonResponse
    {
        $request->validate([
            'id' => 'required|exists:lab_reports,id',
        ]);

        try {
            $labReport = LabReport::findOrFail($request->id);

            // Check if user has permission to unarchive
            if(auth()->user()->type == UserType::ADMIN || auth()->user()->type == UserType::MANAGER) {
                $labReport->is_user_archived = 0;
                $labReport->save();

                return response()->json([
                    'success' => true,
                    'message' => 'Item successfully unarchived',
                ]);
            } elseif(auth()->user()->type == UserType::TENANT) {
                // Check if the tenant owns this lab report
                if($labReport->tenant_id == auth()->user()->id) {
                    $labReport->is_tenant_archived = 0;
                    $labReport->save();

                    return response()->json([
                        'success' => true,
                        'message' => 'Item successfully unarchived',
                    ]);
                } else {
                    return response()->json([
                        'success' => false,
                        'message' => 'You can only unarchive your own items',
                    ], 403);
                }
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'You do not have permission to unarchive items',
                ], 403);
            }
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to unarchive item: ' . $e->getMessage(),
            ], 500);
        }
    }
}
