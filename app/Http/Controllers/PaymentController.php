<?php

namespace App\Http\Controllers;

use App\Constants\LabReportStatus;
use App\Constants\UserType;
use App\Http\Services\BillGeneration;
use App\Http\Services\CacheService;
use App\Models\LabReport;
use App\Models\PaymentTransaction;
use App\Models\User;
use Carbon\Carbon;
use DB;
use Illuminate\Http\Request;

class PaymentController extends Controller
{
    public function index(Request $request)
    {
        view()->share('page', config('nav.payment'));

        if(auth()->user()->type == UserType::ADMIN || auth()->user()->type == UserType::MANAGER) {
            $cacheService = new CacheService();

            $tenants = $cacheService->getTenants();
            $radiologists = $cacheService->getRadiologists();

            $transactions = PaymentTransaction::query()
                ->orderBy('id', 'desc');

            if($request->has('tenant') && $request->tenant > 0) {
                $transactions = $transactions->where('user_id', $request->tenant);
            }else if($request->has('radiologist') && $request->radiologist > 0) {
                $transactions = $transactions->where('user_id', $request->radiologist);
            }

            if($request->has('status') && $request->status != '') {
                $transactions = $transactions->where('status', $request->status);
            }

            if($request->has('year') && $request->year > 0 && $request->has('month') && $request->month > 0) {
                $transactions = $transactions->where('payment_month', $request->year . '-' . $request->month);
            }

            $transactions = $transactions->with('user')->paginate(100);

            return view('payment.user', compact('transactions', 'tenants', 'radiologists'));
        }else if(auth()->user()->type == UserType::TENANT) {
            $transactions = PaymentTransaction::query()
                ->where('user_id', auth()->user()->id)
                ->where('status', '!=', 'PENDING')
                ->orderBy('id', 'desc')
                ->paginate(100);

            return view('payment.tenant', compact('transactions'));
        }else if(auth()->user()->type == UserType::RADIOLOGIST) {
            $transactions = PaymentTransaction::query()
                ->where('user_id', auth()->user()->id)
                ->where('status', '!=', 'PENDING')
                ->orderBy('id', 'desc')
                ->paginate(100);

            return view('payment.radiologist', compact('transactions'));
        }else return 'Access Denied';
    }

    public function details($id) {
        view()->share('page', config('nav.payment'));

        $transaction = PaymentTransaction::find($id);
        $worklist = LabReport::query()->whereIn('id', json_decode($transaction->lab_report_ids))
            ->with('tenant','completedBy')
            ->get();
        return view('payment.details', compact('transaction', 'worklist'));
    }

    public function updateTransactionStatus(Request $request) {
        try{
            $request->validate([
                'transaction_id'    => 'required',
                'old_status'        => 'required|in:PENDING,DUE',
                'new_status'        => 'required|in:DUE,PAID',
            ]);

            $transaction = PaymentTransaction::findOrFail($request->transaction_id);
            if($transaction->status == $request->old_status && $request->new_status == 'DUE') {
                $transaction->status = 'DUE';
                $transaction->approved_by = auth()->user()->id;
                $transaction->approved_at = now();
                $transaction->save();

                return response()->json([
                    'success' => true,
                    'message' => 'Transaction confirmed successfully',
                ]);
            }else if($transaction->status == $request->old_status && $request->new_status == 'PAID') {
                $transaction->status = 'PAID';
                $transaction->paid_at = now();
                $transaction->payment_method = $request->payment_method;
                $transaction->save();

                return response()->json([
                    'success' => true,
                    'message' => 'Transaction confirmed successfully',
                ]);
            }else {
                return response()->json([
                    'success' => false,
                    'message' => 'Transaction not found!',
                ]);
            }
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update status of transaction',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function generateBill(Request $request, BillGeneration $billGeneration) {
        try {
            $request->validate([
                'user_id'           => 'required',
                'payment_month'     => 'required',
                'user_type'         => 'required|in:TENANT,RADIOLOGIST',
            ]);

            $transaction = PaymentTransaction::where('user_id', $request->user_id)
                ->where('user_type', $request->user_type)
                ->where('payment_month', $request->payment_month)
                ->first();

            if($transaction) {
                return response()->json([
                    'success' => false,
                    'message' => 'Bill already generated for this month. You can regenerate the bill from the payment page.',
                ]);
            }

            if($request->user_type == UserType::TENANT) {
                $user = User::find($request->user_id);
                $lastMonthLabCompletedReports = DB::table('lab_reports')
                    ->select('id', 'tenant_id', 'tenant_bill')
                    ->where('status', LabReportStatus::COMPLETED)
                    ->where('tenant_id', $user->id)
                    ->where('completed_at', '>=', Carbon::createFromFormat('Y-m', $request->payment_month)->startOfMonth())
                    ->where('completed_at', '<=', Carbon::createFromFormat('Y-m', $request->payment_month)->endOfMonth())
                    ->get();

                $total_amount = $lastMonthLabCompletedReports->sum('tenant_bill');
                if(empty($total_amount) || $total_amount == 0) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Bill amount is 0',
                    ]);
                }

                $billGeneration->generateTenantBill($user, $lastMonthLabCompletedReports, $total_amount, $request->payment_month);

                return response()->json([
                    'success' => true,
                    'message' => 'Bill generated successfully. You can see the details from the payment page.',
                ]);
            }else if($transaction->user_type == UserType::RADIOLOGIST) {
                $user = User::find($request->user_id);

                if($user->is_internal == 1) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Bill cannot be generated for internal Radiologist',
                    ]);
                }

                $lastMonthLabCompletedReports = DB::table('lab_reports')
                    ->select('id', 'completed_by', 'radiologist_bill')
                    ->where('status', LabReportStatus::COMPLETED)
                    ->where('completed_by', $user->id)
                    ->where('completed_at', '>=', Carbon::createFromFormat('Y-m', $request->payment_month)->startOfMonth())
                    ->where('completed_at', '<=', Carbon::createFromFormat('Y-m', $request->payment_month)->endOfMonth())
                    ->get();

                $total_amount = $lastMonthLabCompletedReports->sum('radiologist_bill');
                if(empty($total_amount) || $total_amount == 0) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Bill amount is 0',
                    ]);
                }

                $billGeneration->generateRadiologistBill($user, $lastMonthLabCompletedReports, $total_amount, $request->payment_month);

                return response()->json([
                    'success' => true,
                    'message' => 'Bill generated successfully. You can see the details from the payment page.',
                ]);
            }
        }catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to regenerate bill',
                'error' => $e->getMessage()
            ], 500);
        }

    }

    public function regenerateBill(Request $request, BillGeneration $billGeneration) {
        try {
            $request->validate([
                'transaction_id' => 'required'
            ]);

            $transaction = PaymentTransaction::findOrFail($request->transaction_id);

            if($transaction->user_type == UserType::TENANT) {
                $user = User::find($transaction->user_id);
                $payment_month = $transaction->payment_month;
                $lastMonthLabCompletedReports = DB::table('lab_reports')
                    ->select('id', 'tenant_id', 'tenant_bill')
                    ->where('status', LabReportStatus::COMPLETED)
                    ->where('tenant_id', $user->id)
                    ->where('completed_at', '>=', Carbon::createFromFormat('Y-m', $transaction->payment_month)->startOfMonth())
                    ->where('completed_at', '<=', Carbon::createFromFormat('Y-m', $transaction->payment_month)->endOfMonth())
                    ->get();

                $total_amount = $lastMonthLabCompletedReports->sum('tenant_bill');
                if(empty($total_amount) || $total_amount == 0) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Bill amount is 0',
                    ]);
                }

                $transaction->delete();
                $billGeneration->generateTenantBill($user, $lastMonthLabCompletedReports, $total_amount, $payment_month);

                return response()->json([
                    'success' => true,
                    'message' => 'Bill regenerated successfully',
                ]);
            }else if($transaction->user_type == UserType::RADIOLOGIST) {
                $user = User::find($transaction->user_id);
                $payment_month = $transaction->payment_month;
                $completed_reports = DB::table('lab_reports')
                    ->select('id', 'completed_by', 'radiologist_bill')
                    ->where('status', LabReportStatus::COMPLETED)
                    ->where('completed_by', $user->id)
                    ->where('completed_at', '>=', Carbon::createFromFormat('Y-m', $transaction->payment_month)->startOfMonth())
                    ->where('completed_at', '<=', Carbon::createFromFormat('Y-m', $transaction->payment_month)->endOfMonth())
                    ->get();

                $total_amount = $completed_reports->sum('radiologist_bill');
                if(empty($total_amount) || $total_amount == 0) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Bill amount is 0',
                    ]);
                }

                $transaction->delete();
                $billGeneration->generateRadiologistBill($user, $completed_reports, $total_amount, $payment_month);

                return response()->json([
                    'success' => true,
                    'message' => 'Bill regenerated successfully',
                ]);
            }
        }catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to regenerate bill',
                'error' => $e->getMessage()
            ], 500);
        }

    }


}
