<?php

namespace App\Http\Controllers;

use App\Constants\UserType;
use App\Http\Services\CacheService;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use DB;
use Hash;

class UserController extends Controller
{
    public function index(Request $request) {
        view()->share('page', config('nav.users'));

        $search = $request->input('search') ?? null;
        $filterType = $request->input('filterType') ?? 'all';

        $users = User::query()->orderBy('name', 'asc');
        if($search != null) {
            $users->where(function ($query) use ($search) {
                $query->where('name', 'like', "%{$search}%")
                    ->orWhere('username', 'like', "%{$search}%")
                    ->orWhere('email', 'like', "%{$search}%")
                    ->orWhere('mobile', 'like', "%{$search}%");
            });
        }
        if($request->has('filterType') && $filterType != 'all') {
            if($filterType == 'USER') {
                $users->whereIn('type', ['ADMIN', 'MANAGER','SALES']);
            }else{
                $users->where('type', $filterType);
            }
        }

        $users = $users->get();
        return view('user.index', compact('users'));
    }

    public function addUserView(Request $request) {
        view()->share('page', config('nav.users'));
        return view('user.add_user');
    }

    public function addUser(Request $request) {
        $validator = Validator::make($request->all(), [
            'username'  => ['required', 'string', 'max:30', 'unique:users,username', 'regex:/^[a-zA-Z0-9_]+$/',],
            'name'      => 'required|string|max:100',
            'mobile'    => 'required|string|max:14',
            'email'     => 'nullable|email|max:100',
            'password'  => 'required|string|min:6',
            'type'      => 'required|in:ADMIN,MANAGER,SALES',
            'status'    => 'required|in:ACTIVE,INACTIVE',
        ], [
            'username.regex' => 'Username can only contain letters, numbers and underscores.',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        if($request->password != $request->confirm_password) {
            return redirect()->back()
                ->with('error', 'Password and password confirmation do not match.' . $request->password . ' ' . $request->confirm_password)
                ->withInput();
        }

        try {
            $user = new User();
            $user->username         = $request->username;
            $user->name             = $request->name;
            $user->mobile           = $request->mobile;
            $user->email            = $request->email;
            $user->password         = Hash::make($request->password);
            $user->type             = $request->type;
            $user->identity_info    = $request->identity_info;
            $user->status           = $request->status;
            $user->save();

            return redirect()->back()
                ->with('success', 'User account created successfully.');

        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Error creating user: ' . $e->getMessage())
                ->withInput();
        }
    }

    public function updateUserView(Request $request, int $id) {
        view()->share('page', config('nav.users'));
        $user = User::findOrFail($id);
        return view('user.update_user', compact('user'));
    }

    public function updateUser(Request $request, int $id) {
        $validator = Validator::make($request->all(), [
            'username'  => 'required|string|max:30',
            'name'      => 'required|string|max:100',
            'mobile'    => 'required|string|max:14',
            'email'     => 'nullable|email|max:100',
            'password'  => 'nullable|string|min:6',
            'type'      => 'required|in:ADMIN,MANAGER,SALES',
            'status'    => 'required|in:ACTIVE,INACTIVE',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        if($request->has('password') && $request->password != $request->confirm_password) {
            return redirect()->back()
                ->with('error', 'Password and password confirmation do not match.' . $request->password . ' ' . $request->confirm_password)
                ->withInput();
        }

        if(DB::table('users')->where('username', $request->username)->where('id', '!=', $id)->exists()) {
            return redirect()->back()
                ->with('error', 'Username already exists.')
                ->withInput();
        }

        try {

            $user = User::findOrFail($id);
            $user->username     = $request->username;
            $user->name         = $request->name;
            $user->mobile       = $request->mobile;
            $user->email        = $request->email;
            if($request->has('password') && $request->filled('password')) {
                $user->password = Hash::make($request->password);
            }
            $user->identity_info= $request->identity_info;
            $user->type         = $request->type;
            $user->status       = $request->status;
            $user->save();

            return redirect()->back()
                ->with('success', 'User account updated successfully.');

        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Error updating user: ' . $e->getMessage())
                ->withInput();
        }
    }

    public function addTenantView(Request $request, CacheService $cacheService) {
        view()->share('page', config('nav.users'));
        $modalities = $cacheService->getModalityTemplates();

        $upazilas = $cacheService->getAllUpazillas();
        $districts = $cacheService->getAllDistricts();
        $divisions = $cacheService->getAllDivisions();

        return view('user.add_tenant', compact('modalities','upazilas','districts','divisions'));
    }

    public function addTenant(Request $request) {
        $validator = Validator::make($request->all(), [
            'username'      => ['required', 'string', 'max:30', 'unique:users,username', 'regex:/^[a-zA-Z0-9_]+$/',],
            'name'          => 'required|string|max:100',
            'mobile'        => 'nullable|string|max:14',
            'email'         => 'nullable|email|max:100',
            'fax'           => 'nullable|string|max:30',
            'contact_name'  => 'nullable|string|max:50',
            'contact_mobile'=> 'nullable|string|max:14',
            'address'       => 'nullable|string|max:255',
            'password'      => 'required|string|min:6',
            'status'        => 'required|in:ACTIVE,INACTIVE',
            'rates.*'       => 'required|numeric|min:1',
        ], [
            'username.regex' => 'Username can only contain letters, numbers and underscores.',
            'rates.*.required' => 'Rate is required for each modality.',
            'rates.*.numeric' => 'Rate must be a valid number.',
            'rates.*.min' => 'Rate must be greater than 0.',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        if($request->password != $request->confirm_password) {
            return redirect()->back()
                ->with('error', 'Password and password confirmation do not match.' . $request->password . ' ' . $request->confirm_password)
                ->withInput();
        }

        try {
            $user = new User();
            $user->username         = $request->username;
            $user->name             = $request->name;
            $user->mobile           = $request->mobile;
            $user->email            = $request->email;
            $user->password         = Hash::make($request->password);
            $user->type             = UserType::TENANT;
            $user->status           = $request->status;
            $user->fax              = $request->fax;
            $user->contact_name     = $request->contact_name;
            $user->contact_mobile   = $request->contact_mobile;
            $user->address          = $request->address;
            $user->upazila          = $request->upazila;
            $user->district         = $request->district;
            $user->division         = $request->division;
            $user->modalities_n_rates      = json_encode(array_filter($request->input('rates'), function ($value) {
                return $value !== null && $value !== '';
            }));
            $user->save();

            (new CacheService())->getTenants(true);

            return redirect()->back()
                ->with('success', 'Tenant account created successfully.');

        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Error creating tenant: ' . $e->getMessage())
                ->withInput();
        }
    }

    public function updateTenantView(Request $request, int $id, CacheService $cacheService) {
        view()->share('page', config('nav.users'));
        $user = User::findOrFail($id);
        $modalities = $cacheService->getModalityTemplates();
        $upazilas = $cacheService->getAllUpazillas();
        $districts = $cacheService->getAllDistricts();
        $divisions = $cacheService->getAllDivisions();
        return view('user.update_tenant', compact('user', 'modalities','upazilas','districts','divisions'));
    }

    public function updateTenant(Request $request, int $id) {
        $validator = Validator::make($request->all(), [
            'username'      => 'required|string|max:30',
            'name'          => 'required|string|max:100',
            'mobile'        => 'nullable|string|max:14',
            'email'         => 'nullable|email|max:100',
            'fax'           => 'nullable|string|max:30',
            'contact_name'  => 'nullable|string|max:50',
            'contact_mobile'=> 'nullable|string|max:14',
            'address'       => 'nullable|string|max:255',
            'password'      => 'nullable|string|min:6',
            'status'        => 'required|in:ACTIVE,INACTIVE',
            'rates.*'       => 'required|numeric|min:1',
        ], [
            'rates.*.required' => 'Rate is required for each modality.',
            'rates.*.numeric' => 'Rate must be a valid number.',
            'rates.*.min' => 'Rate must be greater than 0.',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        if($request->has('password') && $request->password != $request->confirm_password) {
            return redirect()->back()
                ->with('error', 'Password and password confirmation do not match.' . $request->password . ' ' . $request->confirm_password)
                ->withInput();
        }

        if(DB::table('users')->where('username', $request->username)->where('id', '!=', $id)->exists()) {
            return redirect()->back()
                ->with('error', 'Username already exists.')
                ->withInput();
        }

        try {
            $user = User::findOrFail($id);
            $user->username         = $request->username;
            $user->name             = $request->name;
            $user->mobile           = $request->mobile;
            $user->email            = $request->email;
            if($request->has('password') && $request->filled('password')) {
                $user->password = Hash::make($request->password);
            }
            $user->status           = $request->status;
            $user->fax              = $request->fax;
            $user->contact_name     = $request->contact_name;
            $user->contact_mobile   = $request->contact_mobile;
            $user->address          = $request->address;
            $user->upazila          = $request->upazila;
            $user->district         = $request->district;
            $user->division         = $request->division;
            $user->modalities_n_rates      = json_encode(array_filter($request->input('rates'), function ($value) {
                return $value !== null && $value !== '' && $value !== '0' && $value !== '0.00';
            }));
            $user->save();

            (new CacheService())->getTenants(true);

            return redirect()->back()
                ->with('success', 'User account updated successfully.');

        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Error updating user: ' . $e->getMessage())
                ->withInput();
        }
    }


    public function addRadiologistView(Request $request, CacheService $cacheService) {
        view()->share('page', config('nav.users'));
        $modalities = $cacheService->getModalityTemplates();
        return view('user.add_radiologist', compact('modalities'));
    }

    public function addRadiologist(Request $request) {
        $validator = Validator::make($request->all(), [
            'username'      => ['required', 'string', 'max:30', 'unique:users,username', 'regex:/^[a-zA-Z0-9_]+$/',],
            'name'          => 'required|string|max:100',
            'mobile'        => 'nullable|string|max:14',
            'email'         => 'nullable|email|max:100',
            'identity_info' => 'nullable|string|max:255',
            'bmdc_number'   => 'nullable|string|min:20',
            'password'      => 'required|string|min:6',
            'status'        => 'required|in:ACTIVE,INACTIVE',
            'internal'      => 'required|in:NO,YES',
            'signature'     => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'rates.*'       => 'required|numeric|min:1',
        ], [
            'username.regex' => 'Username can only contain letters, numbers and underscores.',
            'rates.*.required' => 'Rate is required for each modality.',
            'rates.*.numeric' => 'Rate must be a valid number.',
            'rates.*.min' => 'Rate must be greater than 0.',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        if($request->password != $request->confirm_password) {
            return redirect()->back()
                ->with('error', 'Password and password confirmation do not match.' . $request->password . ' ' . $request->confirm_password)
                ->withInput();
        }

        try {
            $user = new User();
            $user->username         = $request->username;
            $user->name             = $request->name;
            $user->mobile           = $request->mobile;
            $user->email            = $request->email;
            $user->password         = Hash::make($request->password);
            $user->type             = UserType::RADIOLOGIST;
            $user->status           = $request->status;
            $user->is_internal      = $request->internal == 'YES' ? true : false;
            $user->identity_info    = $request->identity_info;
            $user->bmdc_number      = $request->bmdc_number;
            $user->modalities_n_rates      = json_encode(array_filter($request->input('rates'), function ($value) {
                return $value !== null && $value !== '';
            }));
                if($request->hasFile('signature')) {
                    $user->signature = $request->file('signature')->store(config('filesystems.prefix') . '/signatures');
                }
            $user->save();

            (new CacheService())->getRadiologists(true);

            return redirect()->back()
                ->with('success', 'Radiologist account created successfully.');

        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Error creating Radiologist: ' . $e->getMessage())
                ->withInput();
        }
    }

    public function updateRadiologistView(Request $request, int $id, CacheService $cacheService) {
        view()->share('page', config('nav.users'));
        $user = User::findOrFail($id);
        $modalities = $cacheService->getModalityTemplates();
        return view('user.update_radiologist', compact('user', 'modalities'));
    }

    public function updateRadiologist(Request $request, int $id) {
        $validator = Validator::make($request->all(), [
            'username'      => 'required|string|max:30',
            'name'          => 'required|string|max:100',
            'mobile'        => 'nullable|string|max:14',
            'email'         => 'nullable|email|max:100',
            'identity_info' => 'nullable|string|max:255',
            'bmdc_number'   => 'nullable|string|max:20',
            'password'      => 'nullable|string|min:6',
            'status'        => 'required|in:ACTIVE,INACTIVE',
            'internal'      => 'required|in:NO,YES',
            'signature'     => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'rates.*'       => 'required|numeric|min:1',
        ], [
            'rates.*.required' => 'Rate is required for each modality.',
            'rates.*.numeric' => 'Rate must be a valid number.',
            'rates.*.min' => 'Rate must be greater than 0.',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        if($request->has('password') && $request->password != $request->confirm_password) {
            return redirect()->back()
                ->with('error', 'Password and password confirmation do not match.' . $request->password . ' ' . $request->confirm_password)
                ->withInput();
        }

        if(DB::table('users')->where('username', $request->username)->where('id', '!=', $id)->exists()) {
            return redirect()->back()
                ->with('error', 'Username already exists.')
                ->withInput();
        }

        try {
            $user = User::findOrFail($id);
            $user->username         = $request->username;
            $user->name             = $request->name;
            $user->mobile           = $request->mobile;
            $user->email            = $request->email;
            if($request->has('password') && $request->filled('password')) {
                $user->password = Hash::make($request->password);
            }
            $user->status           = $request->status;
            $user->is_internal      = $request->internal == 'YES' ? true : false;
            $user->identity_info    = $request->identity_info;
            $user->bmdc_number      = $request->bmdc_number;
            $user->modalities_n_rates      = json_encode($request->input('rates'));
            if($request->hasFile('signature')) {
                $user->signature = $request->file('signature')->store(config('filesystems.prefix') . '/signatures');
            }
            $user->save();

            (new CacheService())->getRadiologists(true);

            return redirect()->back()
                ->with('success', 'Radiologist account updated successfully.');

        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Error updating Radiologist: ' . $e->getMessage())
                ->withInput();
        }
    }

    public function acceptPrivacyPolicyView(Request $request){
        $user = User::findOrFail(auth()->user()->id);
        if($user->type == UserType::TENANT || $user->type == UserType::RADIOLOGIST) {
            $user->is_accepted_pnp = true;
            $user->save();
            return redirect()->back()
                ->with('success', 'Thank you for accepting the "Terms of Service & Privacy Policy"');
        }else{
            return redirect()->back()
                ->with('error', 'Something went wrong.');
        }
    }


}
