<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class LabReport extends Model
{
    public function assignees()
    {
        return $this->belongsToMany(User::class, 'assignees');
    }

    public function completedBy()
    {
        return $this->belongsTo(User::class, 'completed_by');
    }

    public function tenant()
    {
        return $this->belongsTo(User::class, 'tenant_id');
    }

    public function doctor()
    {
        return $this->belongsTo(Doctor::class);
    }
}
