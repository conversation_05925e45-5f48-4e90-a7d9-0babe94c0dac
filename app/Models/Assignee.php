<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Assignee extends Model
{
    protected $fillable = [
        'lab_report_id',
        'user_id'
    ];

    public function labReport(): BelongsTo
    {
        return $this->belongsTo(LabReport::class);
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
}
