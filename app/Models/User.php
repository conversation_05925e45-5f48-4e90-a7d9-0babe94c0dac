<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Foundation\Auth\User as AuthTable;

class User extends AuthTable
{
    protected $guarded = [ 'id' ];
    protected $hidden  = [ 'password' ];

    public function lab_reports()
    {
        return $this->belongsToMany(LabReport::class, 'assignees');
    }

    protected function modalitiesNRates(): Attribute
    {
        return Attribute::make(
            get: fn (mixed $modalities_n_rates): mixed => json_decode($modalities_n_rates),
        );
    }

}
