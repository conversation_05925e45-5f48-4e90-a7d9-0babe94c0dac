<?php

namespace App\Events;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class JobAssignNotification implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $message;
    public $radiologist_username;

    public function __construct(string $message, string $radiologist_username)
    {
        $this->message = $message;
        $this->radiologist_username = $radiologist_username;
    }

    public function broadcastOn()
    {
        return config('app.env').'.bms';
    }

    public function broadcastAs()
    {
        return 'job_assigned.' . $this->radiologist_username;
    }
}
