<?php

namespace App\Events;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class JobCompletionNotification implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $message;
    public $tenant_username;

    public function __construct(string $message, string $tenant_username)
    {
        $this->message = $message;
        $this->tenant_username = $tenant_username;
    }

    public function broadcastOn()
    {
        return config('app.env') . '.bms';
    }

    public function broadcastAs()
    {
        return 'job_completed';
    }
}
