<?php

namespace App\Console\Commands;

use App\Constants\LabReportStatus;
use App\Constants\UserType;
use App\Http\Controllers\PaymentController;
use App\Http\Services\BillGeneration;
use App\Models\PaymentTransaction;
use App\Models\User;
use Illuminate\Console\Command;
use Log;
use DB;

class GeneratePaymentTransaction extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:generate-payment-transaction';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    protected BillGeneration $billGeneration;

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->billGeneration = new BillGeneration();
        $lastMonthLabCompletedReports = DB::table('lab_reports')
            ->select('id','status','parent_procedure','tenant_id','completed_by','radiologist_bill','tenant_bill')
            ->where('status', LabReportStatus::COMPLETED)
            ->where('completed_at', '>=', date('Y-m-d', strtotime('first day of last month')) . ' 00:00:00')
            ->where('completed_at', '<=', date('Y-m-d', strtotime('last day of last month')) . ' 23:59:59')
            ->get();

        $this->generateRadiologistPayment($lastMonthLabCompletedReports);
        $this->generateTenantPayment($lastMonthLabCompletedReports);
    }

    private function generateRadiologistPayment($lastMonthLabCompletedReports): void{
        $radiologist = User::query()->select('id', 'modalities_n_rates')->where('type', UserType::RADIOLOGIST)->get();
        foreach ($radiologist as $user) {
            if($user->is_internal) {
                continue;
            }
            try{
                if(PaymentTransaction::query()->where('user_id', $user->id)->where('payment_month', date('Y-m', strtotime('first day of last month')))->exists()) {
                    continue;
                }

                $completed_reports = $lastMonthLabCompletedReports->where('completed_by', $user->id);
                $total_amount = $completed_reports->sum('radiologist_bill');

                if(empty($total_amount) || $total_amount == 0) {
                    continue;
                }
                $payment_month = date('Y-m', strtotime('first day of last month'));

                $this->billGeneration->generateRadiologistBill(user: $user, completed_reports: $completed_reports, total_amount: $total_amount, payment_month: $payment_month);
            }catch (\Exception $ex){
                Log::error('generateRadiologistPayment => '. $ex->getMessage());
                $this->error('generateRadiologistPayment => '. $ex->getMessage());
            }
        }
    }

    private function generateTenantPayment($lastMonthLabCompletedReports): void{
        $tenants = User::query()->select('id', 'name', 'modalities_n_rates')->where('type', UserType::TENANT)->get();
        foreach ($tenants as $user) {
            try{
                if(PaymentTransaction::query()->where('user_id', $user->id)->where('payment_month', date('Y-m', strtotime('first day of last month')))->exists()) {
                    continue;
                }

                $completed_reports = $lastMonthLabCompletedReports->where('tenant_id', $user->id)->where('tenant_bill', '>', 0);
                $total_amount = $completed_reports->sum('tenant_bill');;
                if(empty($total_amount) || $total_amount == 0) {
                    continue;
                }

                $payment_month = date('Y-m', strtotime('first day of last month'));

                $this->billGeneration->generateTenantBill(user: $user, completed_reports: $completed_reports, total_amount: $total_amount, payment_month: $payment_month);
            }catch (\Exception $ex){
                Log::error('generateTenantPayment => '. $ex->getMessage());
                $this->error('generateTenantPayment => '. $ex->getMessage());
            }
        }
    }

}
