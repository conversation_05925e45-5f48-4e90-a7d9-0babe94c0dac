<?php

namespace App\Console\Commands;

use App\Constants\LabReportStatus;
use App\Constants\UserType;
use App\Events\NewJobPlaceNotification;
use App\Models\LabReport;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use mmerlijn\dicom\Nanodicom;
use DB;

class FetchSpaceToEntryLabReport extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:fetch-space-to-entry-lab-report';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fetch files from Spaces and process lab reports';

    protected const fresh_folder_name       = 'fresh';
    protected const archived_folder_name    = 'archived';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            $users = DB::table('users')
                ->select('id','username')
                ->where('type', UserType::TENANT)
                ->get();

            foreach ($users as $user) {
                $folderPath = config('filesystems.prefix') . '/' . self::fresh_folder_name . '/' . $user->username . '/';

                $files = Storage::files($folderPath);

                if (empty($files)) {
                    continue;
                }

                foreach ($files as $file) {
                    $this->processFile($file, $user->id, $user->username);
                }
            }
        } catch (\Exception $e) {
            Log::error('Error in FetchSpaceToEntryLabReport command: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);
        }

        return 0;
    }

    /**
     * Process a single file from the Space
     *
     * @param string $filePath
     * @return void
     */
    protected function processFile(string $filePath, int $user_id, string $username)
    {
        if (!Storage::exists($filePath) || Storage::size($filePath) === false || !preg_match('/\.dcm$/i', $filePath)) {
            if(Storage::exists($filePath)) {
                Storage::delete($filePath);
            }
            return;
        }

        $fileName =  $username. time() . rand(1,99).'.'.substr($filePath, strrpos($filePath, '.') + 1); //basename($filePath);

        try{
            $newPath = config('filesystems.prefix') . '/' . self::archived_folder_name . '/' . $fileName;

            $data = $this->getMetadata($filePath);

            $LabReport = new LabReport();
            $LabReport->patient_id          = $data->patient_id;
            $LabReport->patient_name        = $data->patient_name;
            $LabReport->patient_age         = $data->patient_age;
            $LabReport->patient_gender      = $data->patient_gender;
            $LabReport->procedure           = $data->procedure_name;
            $LabReport->procedure_history   = '';
            $LabReport->clinical_history    = $data->clinical_history;
            $LabReport->modality            = $data->modality;
            $LabReport->tenant_id           = $user_id;
            $LabReport->file_links          = $newPath;
            $LabReport->study_time          = Carbon::createFromFormat('Ymd', $data->ordered_at)->format('Y-m-d');
            $LabReport->status              = LabReportStatus::UNASSIGNED;
            $LabReport->saveOrFail();

            event(new NewJobPlaceNotification(
                message: 'New Report has been added in Worklist',
                tenant_username: $username
            ));

            Storage::move($filePath, $newPath);
        }catch (\Exception $ex){
            Log::error('Error processing file: ' . $filePath);
            //Storage::delete($filePath);
        }
    }

    private function getMetadata(string $filePath): object
    {
        $tempFile = storage_path('app/temp.dcm');

        // Download from S3/local disk
        $stream = Storage::readStream($filePath);
        file_put_contents($tempFile, stream_get_contents($stream));

        // Define required DICOM tags
        $requiredTags = [
            'patient_id'        => [0x0010, 0x0020], // PatientID
            'patient_name'      => [0x0010, 0x0010], // PatientName
            'modality'          => [0x0008, 0x0060], // Modality
            'procedure_name'    => [0x0018, 0x0015], // ProcedureCodeSequence
            'clinical_history'  => [0x0008, 0x1030], // ClinicalHistory
            'ordered_at'        => [0x0008, 0x0020], // StudyDate
            'patient_age'       => [0x0010, 0x1010], // PatientAge
            'patient_gender'    => [0x0010, 0x0040], // PatientSex
        ];

        $dicom = Nanodicom::factory($tempFile, 'simple');
        $dicom->parse(array_values($requiredTags));

        $data = [];

        foreach ($requiredTags as $key => [$group, $element]) {
            $value = $dicom->value($group, $element) ?? null;
            $data[$key] = is_array($value) ? implode(', ', $value) : $value;
        }

        // Cleanup temp file
        @unlink($tempFile);

        return (object) $data;
    }
}
