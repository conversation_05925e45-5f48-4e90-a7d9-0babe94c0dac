<?php

use App\Http\Controllers\ArchivedLabReportController;
use App\Http\Controllers\PaymentController;
use App\Http\Controllers\UserController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\LoginController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\LabReportController;

Route::get('/login', [LoginController::class, 'setLoginView'])->name('login');
Route::post('auth/login', [LoginController::class, 'getLogin']);

Route::middleware('auth')->group(function () {
    Route::get('auth/logout', [ProfileController::class, 'logout']);
    Route::get('profile', [ProfileController::class, 'profile']);
    Route::post('profile', [ProfileController::class, 'profileUpdate']);

    Route::get('/', [LabReportController::class, 'worklist'])->name('worklist');
    Route::prefix('worklist')->controller(LabReportController::class)->group(function () {
        Route::get('/', 'worklist');

        Route::get('/add-job', 'addJobView');
        Route::post('/add-job', 'addJob');
        Route::post('/add-doctor', 'addDoctor');
        Route::post('/add-clinical-history', 'addClinicalHistory');

        Route::post('/update-clinical-history', 'updateClinicalHistory')->name('worklist.update-clinical-history');
        Route::post('/update-procedure', 'updateProcedure')->name('worklist.update-procedure');
        Route::post('/bulk-assign', 'bulkAssignRadiologists')->name('worklist.bulk-assign');
        Route::post('/bulk-archive', 'bulkArchive')->name('worklist.bulk-archive');
        Route::post('/merge', 'merge')->name('worklist.merge');

        Route::get('/{id}/dcm-view', 'dcmFileView');
        Route::get('/{id}/view', 'fileView');
        Route::post('/{id}/save-feedback', 'saveFeedback');
        Route::post('/save-feedback-template', 'saveFeedbackTemplate');

        Route::get('/{id}/print', 'print');
    });

    Route::controller(ArchivedLabReportController::class)->group(function () {
        Route::get('/archive', 'archivedList');
        Route::post('/archive/unarchive', 'unarchive')->name('archive.unarchive');
    });

    Route::prefix('users')->controller(UserController::class)->group(function () {
        Route::get('/', 'index')->name('users.index');
        Route::get('/add-user', 'addUserView');
        Route::post('/add-user', 'addUser');
        Route::get('/update-user/{id}', 'updateUserView');
        Route::post('/update-user/{id}', 'updateUser');
        Route::get('/accept-privacy-policy', 'acceptPrivacyPolicyView');

        Route::get('/add-tenant', 'addTenantView');
        Route::post('/add-tenant', 'addTenant');
        Route::get('/update-tenant/{id}', 'updateTenantView');
        Route::post('/update-tenant/{id}', 'updateTenant');

        Route::get('/add-radiologist', 'addRadiologistView');
        Route::post('/add-radiologist', 'addRadiologist');
        Route::get('/update-radiologist/{id}', 'updateRadiologistView');
        Route::post('/update-radiologist/{id}', 'updateRadiologist');
    });

    Route::prefix('payment')->controller(PaymentController::class)->group(function () {
        Route::get('/', 'index')->name('payment.index');
        Route::get('/{id}/details', 'details');
        Route::post('/update-transaction-status', 'updateTransactionStatus');
        Route::post('/bill-regenerate', 'regenerateBill');
        Route::post('/bill-generate', 'generateBill');
    });
});
